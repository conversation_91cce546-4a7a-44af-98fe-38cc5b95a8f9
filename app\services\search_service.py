"""
Search Service
=============

Service layer for integrating with existing ChromaDB search functionality.
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor

from app.config import get_settings

# Try to import the advanced search, fallback if not available
try:
    from advanced_product_search import AdvancedProductSearcher
    ADVANCED_SEARCH_AVAILABLE = True
except ImportError:
    ADVANCED_SEARCH_AVAILABLE = False
    AdvancedProductSearcher = None

logger = logging.getLogger(__name__)
settings = get_settings()


class SearchService:
    """
    Service for handling product search operations.
    
    This service integrates with the existing AdvancedProductSearcher
    and provides async interfaces for the FastAPI application.
    """
    
    def __init__(self):
        """Initialize the search service."""
        self.searcher = None
        self.is_initialized = False
        self.executor = ThreadPoolExecutor(max_workers=settings.max_concurrent_searches)
        
    async def initialize(self):
        """Initialize the search service with ChromaDB connection."""
        if self.is_initialized:
            return

        logger.info("Initializing search service...")

        if not ADVANCED_SEARCH_AVAILABLE:
            logger.warning("Advanced search not available, search service will be limited")
            self.is_initialized = True
            return

        # Initialize in thread pool since ChromaDB operations are synchronous
        loop = asyncio.get_event_loop()

        def _init_searcher():
            searcher = AdvancedProductSearcher(
                model_name=settings.embedding_model_name,
                db_path=settings.chroma_db_path
            )
            searcher.load_embedding_model()
            searcher.connect_to_database(settings.chroma_collection_name)
            return searcher

        try:
            self.searcher = await loop.run_in_executor(self.executor, _init_searcher)
            self.is_initialized = True
            logger.info(f"Search service initialized with {self.searcher.collection.count():,} products")
        except Exception as e:
            logger.error(f"Failed to initialize search service: {e}")
            self.is_initialized = True  # Mark as initialized to prevent retries
    
    async def semantic_search(
        self,
        query: str,
        max_results: int = None,
        similarity_threshold: float = None
    ) -> Dict[str, Any]:
        """
        Perform semantic search on products.

        Args:
            query: Search query string
            max_results: Maximum number of results to return
            similarity_threshold: Similarity threshold for high-confidence matches

        Returns:
            Dictionary containing categorized search results
        """
        if not self.is_initialized:
            await self.initialize()

        if not ADVANCED_SEARCH_AVAILABLE or not self.searcher:
            # Return empty results if advanced search is not available
            return {
                'query': query,
                'exact_matches': [],
                'high_confidence_similar': [],
                'medium_confidence_similar': [],
                'category_related': [],
                'total_results': 0,
                'performance': {
                    'search_duration_ms': 0,
                    'total_results_processed': 0,
                    'similarity_threshold': similarity_threshold or 0.3,
                    'error': 'Advanced search service not available'
                }
            }

        max_results = max_results or settings.search_max_results
        similarity_threshold = similarity_threshold or settings.similarity_threshold

        start_time = time.time()

        # Run search in thread pool
        loop = asyncio.get_event_loop()

        def _search():
            return self.searcher.cross_brand_similarity_search(
                query=query,
                similarity_threshold=similarity_threshold,
                max_results=max_results * 2  # Get more results for better categorization
            )

        try:
            raw_results = await asyncio.wait_for(
                loop.run_in_executor(self.executor, _search),
                timeout=settings.search_timeout_seconds
            )

            # Categorize results
            categorized_results = self._categorize_search_results(query, raw_results, max_results)

            # Add performance metrics
            search_duration = (time.time() - start_time) * 1000  # Convert to milliseconds
            categorized_results['performance'] = {
                'search_duration_ms': round(search_duration, 2),
                'total_results_processed': len(raw_results.get('all_matches', [])),
                'similarity_threshold': similarity_threshold
            }

            return categorized_results

        except asyncio.TimeoutError:
            logger.error(f"Search timeout for query: {query}")
            raise Exception(f"Search timeout after {settings.search_timeout_seconds} seconds")
        except Exception as e:
            logger.error(f"Search error for query '{query}': {e}")
            raise Exception(f"Search failed: {str(e)}")
    
    async def mpn_search(self, mpn: str) -> Dict[str, Any]:
        """
        Search for products by MPN with brand similarity.
        
        Args:
            mpn: Manufacturer Part Number
            
        Returns:
            Dictionary containing MPN search results grouped by brand
        """
        if not self.is_initialized:
            await self.initialize()
        
        start_time = time.time()
        
        # Run search in thread pool
        loop = asyncio.get_event_loop()
        
        def _mpn_search():
            return self.searcher.exact_mpn_search_with_brand_similarity(mpn)
        
        try:
            results = await asyncio.wait_for(
                loop.run_in_executor(self.executor, _mpn_search),
                timeout=settings.search_timeout_seconds
            )
            
            # Add performance metrics
            search_duration = (time.time() - start_time) * 1000
            results['performance'] = {
                'search_duration_ms': round(search_duration, 2),
                'search_type': 'mpn'
            }
            
            return results
            
        except asyncio.TimeoutError:
            logger.error(f"MPN search timeout for: {mpn}")
            raise Exception(f"MPN search timeout after {settings.search_timeout_seconds} seconds")
        except Exception as e:
            logger.error(f"MPN search error for '{mpn}': {e}")
            raise Exception(f"MPN search failed: {str(e)}")
    
    async def autocomplete_search(self, query: str, max_results: int = None) -> List[Dict[str, Any]]:
        """
        Get autocomplete suggestions for search query.
        
        Args:
            query: Partial search query
            max_results: Maximum number of suggestions
            
        Returns:
            List of autocomplete suggestions
        """
        if not self.is_initialized:
            await self.initialize()
        
        if len(query.strip()) < 2:
            return []
        
        max_results = max_results or settings.autocomplete_max_results
        
        # Use semantic search with lower threshold for autocomplete
        try:
            results = await self.semantic_search(
                query=query,
                max_results=max_results,
                similarity_threshold=0.5  # More lenient for autocomplete
            )
            
            # Extract suggestions from exact matches and high-confidence results
            suggestions = []
            
            # Add exact matches first
            for match in results.get('exact_matches', [])[:max_results//2]:
                suggestions.append({
                    'text': match['name'],
                    'type': 'exact_match',
                    'brand': match['brand'],
                    'mpn': match['mpn'],
                    'category': match['category'],
                    'similarity_score': match['similarity_score']
                })
            
            # Add high-confidence matches
            remaining_slots = max_results - len(suggestions)
            for match in results.get('high_confidence_similar', [])[:remaining_slots]:
                suggestions.append({
                    'text': match['name'],
                    'type': 'similar',
                    'brand': match['brand'],
                    'mpn': match['mpn'],
                    'category': match['category'],
                    'similarity_score': match['similarity_score']
                })
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Autocomplete error for query '{query}': {e}")
            return []
    
    def _categorize_search_results(
        self, 
        query: str, 
        raw_results: Dict[str, Any], 
        max_results: int
    ) -> Dict[str, Any]:
        """
        Categorize search results into different types.
        
        Args:
            query: Original search query
            raw_results: Raw results from ChromaDB search
            max_results: Maximum results per category
            
        Returns:
            Categorized search results
        """
        # Initialize categories
        exact_matches = []
        high_confidence_similar = []
        medium_confidence_similar = []
        category_related = []
        
        query_lower = query.lower()
        
        for match in raw_results.get('all_matches', []):
            distance = match['similarity_distance']
            name_lower = match['name'].lower()
            mpn_lower = match['mpn'].lower()
            
            # Check for exact matches in name or MPN
            if (query_lower in name_lower or query_lower in mpn_lower or 
                name_lower in query_lower or mpn_lower in query_lower):
                exact_matches.append(match)
            elif distance <= 0.3:  # High confidence threshold
                high_confidence_similar.append(match)
            elif distance <= 0.5:  # Medium confidence threshold
                medium_confidence_similar.append(match)
            else:
                category_related.append(match)
        
        return {
            'query': query,
            'exact_matches': exact_matches[:max_results],
            'high_confidence_similar': high_confidence_similar[:max_results],
            'medium_confidence_similar': medium_confidence_similar[:max_results],
            'category_related': category_related[:max_results//2],  # Fewer category-related results
            'total_results': len(raw_results.get('all_matches', [])),
            'brand_distribution': raw_results.get('brand_distribution', {}),
            'similarity_clusters': raw_results.get('similarity_clusters', {})
        }
    
    async def close(self):
        """Close the search service and cleanup resources."""
        if self.executor:
            self.executor.shutdown(wait=True)
        logger.info("Search service closed")
