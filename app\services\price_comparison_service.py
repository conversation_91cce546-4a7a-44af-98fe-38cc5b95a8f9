"""
Price Comparison Service

This service provides price comparison functionality for wishlist items.
It finds exact MPN matches and similar products using the autocomplete service.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional

from app.models.wishlist import WishlistItem

logger = logging.getLogger(__name__)


class PriceComparisonService:
    """
    Service for comparing prices of similar products.
    
    This service finds exact MPN matches and semantically similar products
    using the existing autocomplete service and product database.
    """
    
    def __init__(self):
        """Initialize the price comparison service."""
        pass
    
    async def compare_wishlist_prices(
        self, 
        wishlist_items: List[WishlistItem],
        similarity_threshold: float = 0.3,
        max_alternatives_per_item: int = 10
    ) -> Dict[str, Any]:
        """
        Compare prices for all items in a wishlist.
        
        Args:
            wishlist_items: List of wishlist items to compare
            similarity_threshold: Threshold for semantic similarity matching
            max_alternatives_per_item: Maximum alternatives to find per item
            
        Returns:
            Dictionary containing price comparison results
        """
        logger.info(f"Starting price comparison for {len(wishlist_items)} wishlist items")
        
        comparison_results = []
        
        # Get autocomplete service for searching
        from app.services.autocomplete_service import get_autocomplete_service
        autocomplete_service = await get_autocomplete_service()
        
        # Compare each item
        for item in wishlist_items:
            try:
                logger.info(f"Comparing item: {item.product_name} (MPN: {item.mpn})")
                
                # Search Type 1: Exact MPN Matches
                exact_matches = await self._find_exact_mpn_matches(autocomplete_service, item)
                
                # Search Type 2: Similar Products  
                similar_products = await self._find_similar_products(autocomplete_service, item, max_alternatives_per_item)
                
                item_comparison = {
                    'wishlist_item': self._item_to_dict(item),
                    'exact_mpn_matches': exact_matches,
                    'similar_products': similar_products,
                    'total_exact_matches': len(exact_matches),
                    'total_similar_products': len(similar_products),
                    'error': None
                }
                
                comparison_results.append(item_comparison)
                
            except Exception as e:
                logger.error(f"Error comparing item {item.id}: {e}")
                # Add error result with safe data
                comparison_results.append({
                    'wishlist_item': self._item_to_dict(item),
                    'exact_mpn_matches': [],
                    'similar_products': [],
                    'total_exact_matches': 0,
                    'total_similar_products': 0,
                    'error': str(e)
                })
        
        # Generate overall summary
        total_exact_matches = sum(result['total_exact_matches'] for result in comparison_results)
        total_similar_products = sum(result['total_similar_products'] for result in comparison_results)
        
        return {
            'comparison_results': comparison_results,
            'summary': {
                'total_items_compared': len(wishlist_items),
                'total_exact_matches_found': total_exact_matches,
                'total_similar_products_found': total_similar_products,
                'items_with_exact_matches': len([r for r in comparison_results if r['total_exact_matches'] > 0]),
                'items_with_similar_products': len([r for r in comparison_results if r['total_similar_products'] > 0])
            }
        }

    async def _find_exact_mpn_matches(self, autocomplete_service, item: WishlistItem) -> List[Dict[str, Any]]:
        """Find products with exactly the same MPN."""
        try:
            if not item.mpn:
                return []
                
            # Search for exact MPN matches using autocomplete service
            suggestions = await autocomplete_service.get_suggestions(item.mpn, max_results=50)
            
            exact_matches = []
            for suggestion in suggestions:
                if suggestion.get('match_type') == 'mpn_exact' and suggestion.get('mpn', '').lower() == item.mpn.lower():
                    match_data = {
                        'product_id': suggestion['product_id'],
                        'product_name': suggestion['text'],
                        'brand': suggestion['brand'],
                        'mpn': suggestion['mpn'],
                        'category': suggestion['category'],
                        'seller': self._extract_seller_from_url(suggestion.get('url', '')),
                        'product_url': suggestion.get('url', ''),
                        'price': None,  # Price not available in current data
                        'price_currency': 'USD',
                        'is_original': (suggestion['product_id'] == item.product_id)
                    }
                    exact_matches.append(match_data)
            
            # Sort by seller name for consistent display
            exact_matches.sort(key=lambda x: (x['seller'] or '', x['brand']))
            
            logger.info(f"Found {len(exact_matches)} exact MPN matches for {item.mpn}")
            return exact_matches
            
        except Exception as e:
            logger.error(f"Error finding exact MPN matches for {item.mpn}: {e}")
            return []

    async def _find_similar_products(self, autocomplete_service, item: WishlistItem, max_results: int) -> List[Dict[str, Any]]:
        """Find semantically similar products using product name."""
        try:
            # Search using product name for semantic similarity
            suggestions = await autocomplete_service.get_suggestions(item.product_name, max_results=max_results * 2)
            
            similar_products = []
            for suggestion in suggestions:
                # Skip exact MPN matches (they're handled separately) and the original item
                if (suggestion.get('match_type') == 'mpn_exact' and 
                    suggestion.get('mpn', '').lower() == item.mpn.lower()):
                    continue
                    
                if suggestion['product_id'] == item.product_id:
                    continue
                
                # Calculate similarity score based on match type
                similarity_score = self._calculate_similarity_score(suggestion.get('match_type', 'unknown'))
                
                similar_data = {
                    'product_id': suggestion['product_id'],
                    'product_name': suggestion['text'],
                    'brand': suggestion['brand'],
                    'mpn': suggestion['mpn'],
                    'category': suggestion['category'],
                    'seller': self._extract_seller_from_url(suggestion.get('url', '')),
                    'product_url': suggestion.get('url', ''),
                    'similarity_score': similarity_score,
                    'match_type': suggestion.get('match_type', 'unknown'),
                    'price': None,  # Price not available in current data
                    'price_currency': 'USD'
                }
                similar_products.append(similar_data)
            
            # Sort by similarity score (highest first)
            similar_products.sort(key=lambda x: x['similarity_score'], reverse=True)
            
            # Limit results
            similar_products = similar_products[:max_results]
            
            logger.info(f"Found {len(similar_products)} similar products for {item.product_name}")
            return similar_products
            
        except Exception as e:
            logger.error(f"Error finding similar products for {item.product_name}: {e}")
            return []

    def _extract_seller_from_url(self, url: str) -> str:
        """Extract seller name from product URL."""
        if not url:
            return ''
        
        # Common seller patterns
        if 'henryschein.com' in url:
            return 'Henry Schein'
        elif 'benco.com' in url or 'shop.benco.com' in url:
            return 'Benco'
        elif 'pattersondental.com' in url:
            return 'Patterson'
        elif 'darby.com' in url:
            return 'Darby'
        elif 'amazon.com' in url:
            return 'Amazon'
        else:
            # Try to extract domain name
            try:
                from urllib.parse import urlparse
                domain = urlparse(url).netloc
                return domain.replace('www.', '').replace('.com', '').title()
            except:
                return 'Unknown'

    def _calculate_similarity_score(self, match_type: str) -> float:
        """Calculate similarity score based on match type."""
        scores = {
            'name_prefix': 0.9,
            'name_contains': 0.8,
            'brand': 0.7,
            'fuzzy': 0.6,
            'category': 0.5
        }
        return scores.get(match_type, 0.4)

    def _item_to_dict(self, item: WishlistItem) -> Dict[str, Any]:
        """Convert wishlist item to dictionary."""
        return {
            'id': str(item.id),
            'mpn': item.mpn or '',
            'name': item.product_name or '',
            'brand': item.brand or '',
            'category': item.category or '',
            'subcategory': item.subcategory or '',
            'seller': item.seller or '',
            'price': item.price,
            'price_currency': item.price_currency or 'USD',
            'product_url': item.product_url or ''
        }
