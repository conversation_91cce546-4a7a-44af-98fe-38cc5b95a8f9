"""
Price Comparison Service
=======================

Service for finding and comparing similar products across different sellers and brands.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor
from collections import defaultdict
import statistics

from app.config import get_settings
from app.services.search_service import SearchService
from app.models.wishlist import WishlistItem

logger = logging.getLogger(__name__)
settings = get_settings()


class PriceComparisonService:
    """
    Service for comparing prices of similar products.
    
    This service finds equivalent products across different sellers and brands
    using MPN matching and semantic similarity, then provides price comparison data.
    """
    
    def __init__(self, search_service: SearchService):
        """Initialize the price comparison service."""
        self.search_service = search_service
        self.executor = ThreadPoolExecutor(max_workers=5)
    
    async def compare_wishlist_prices(
        self, 
        wishlist_items: List[WishlistItem],
        similarity_threshold: float = 0.3,
        max_alternatives_per_item: int = 10
    ) -> Dict[str, Any]:
        """
        Compare prices for all items in a wishlist.
        
        Args:
            wishlist_items: List of wishlist items to compare
            similarity_threshold: Threshold for semantic similarity matching
            max_alternatives_per_item: Maximum alternatives to find per item
            
        Returns:
            Dictionary containing price comparison results
        """
        logger.info(f"Starting price comparison for {len(wishlist_items)} wishlist items")
        
        comparison_results = []
        total_savings = 0.0
        total_items_with_alternatives = 0
        
        # Process each wishlist item
        for item in wishlist_items:
            try:
                item_comparison = await self._compare_single_item(
                    item,
                    similarity_threshold,
                    max_alternatives_per_item
                )
                comparison_results.append(item_comparison)

                # Calculate potential savings
                if item_comparison.get('best_alternative') and item.price:
                    best_price = item_comparison['best_alternative'].get('price')
                    if best_price and best_price < item.price:
                        savings = item.price - best_price
                        total_savings += savings
                        total_items_with_alternatives += 1

            except Exception as e:
                logger.error(f"Error comparing item {item.id}: {e}")
                # Add error result with safe data
                comparison_results.append({
                    'wishlist_item': self._item_to_dict(item),
                    'alternatives': [],
                    'mpn_matches': [],
                    'similar_products': [],
                    'best_alternative': None,
                    'price_analysis': None,
                    'total_alternatives_found': 0,
                    'error': str(e)
                })
        
        # Generate overall summary
        summary = self._generate_comparison_summary(comparison_results, total_savings)
        
        return {
            'comparison_results': comparison_results,
            'summary': summary,
            'total_items': len(wishlist_items),
            'items_with_alternatives': total_items_with_alternatives,
            'potential_total_savings': round(total_savings, 2)
        }
    
    async def _compare_single_item(
        self, 
        item: WishlistItem, 
        similarity_threshold: float,
        max_alternatives: int
    ) -> Dict[str, Any]:
        """
        Compare prices for a single wishlist item.
        
        Args:
            item: Wishlist item to compare
            similarity_threshold: Similarity threshold for matching
            max_alternatives: Maximum alternatives to find
            
        Returns:
            Dictionary containing comparison results for the item
        """
        # Step 1: Find exact MPN matches
        mpn_matches = await self._find_mpn_matches(item.mpn)
        
        # Step 2: Find semantically similar products
        similar_products = await self._find_similar_products(
            item, 
            similarity_threshold, 
            max_alternatives
        )
        
        # Step 3: Combine and deduplicate alternatives
        all_alternatives = self._combine_alternatives(mpn_matches, similar_products, item)
        
        # Step 4: Analyze prices and find best deals
        price_analysis = self._analyze_prices(all_alternatives, item)
        
        # Step 5: Find best alternative
        best_alternative = self._find_best_alternative(all_alternatives, item)
        
        return {
            'wishlist_item': self._item_to_dict(item),
            'alternatives': all_alternatives[:max_alternatives],
            'mpn_matches': mpn_matches,
            'similar_products': similar_products,
            'best_alternative': best_alternative,
            'price_analysis': price_analysis,
            'total_alternatives_found': len(all_alternatives)
        }
    
    async def _find_mpn_matches(self, mpn: str) -> List[Dict[str, Any]]:
        """Find products with the same MPN."""
        try:
            # Use autocomplete service to find MPN matches
            from app.services.autocomplete_service import get_autocomplete_service
            autocomplete_service = await get_autocomplete_service()

            # Search for the exact MPN
            suggestions = await autocomplete_service.get_suggestions(mpn, max_results=20)

            matches = []
            for suggestion in suggestions:
                if suggestion.get('match_type') == 'mpn_exact' and suggestion.get('mpn', '').lower() == mpn.lower():
                    matches.append({
                        'type': 'mpn_match',
                        'product': {
                            'product_id': suggestion['product_id'],
                            'name': suggestion['text'],
                            'brand': suggestion['brand'],
                            'mpn': suggestion['mpn'],
                            'category': suggestion['category'],
                            'subcategory': suggestion.get('subcategory', ''),
                            'seller': suggestion.get('seller', ''),
                            'product_url': suggestion.get('url', ''),
                            'similarity_distance': 0.0,
                            'similarity_score': 1.0,
                            'price': None,  # Price not available in autocomplete data
                            'price_currency': 'USD'
                        },
                        'match_confidence': 1.0,  # Exact MPN match
                        'brand_similarity': 0.9  # High brand similarity for same MPN
                    })

            return matches

        except Exception as e:
            logger.error(f"Error finding MPN matches for {mpn}: {e}")
            return []
    
    async def _find_similar_products(
        self,
        item: WishlistItem,
        similarity_threshold: float,
        max_results: int
    ) -> List[Dict[str, Any]]:
        """Find semantically similar products."""
        try:
            # Use autocomplete service to find similar products
            from app.services.autocomplete_service import get_autocomplete_service
            autocomplete_service = await get_autocomplete_service()

            # Create search query from item details
            search_query = f"{item.product_name}"

            # Get suggestions for similar products
            suggestions = await autocomplete_service.get_suggestions(search_query, max_results=max_results * 2)

            similar_products = []

            # Process suggestions to find similar products
            for suggestion in suggestions:
                # Skip if same MPN (already covered by MPN search)
                if suggestion.get('mpn', '').lower() != item.mpn.lower():
                    match_type = suggestion.get('match_type', 'unknown')

                    # Calculate confidence based on match type
                    confidence = {
                        'name_prefix': 0.9,
                        'name_contains': 0.8,
                        'brand': 0.7,
                        'fuzzy': 0.6
                    }.get(match_type, 0.5)

                    similar_products.append({
                        'type': 'semantic_similar',
                        'product': {
                            'product_id': suggestion['product_id'],
                            'name': suggestion['text'],
                            'brand': suggestion['brand'],
                            'mpn': suggestion['mpn'],
                            'category': suggestion['category'],
                            'subcategory': suggestion.get('subcategory', ''),
                            'seller': suggestion.get('seller', ''),
                            'product_url': suggestion.get('url', ''),
                            'similarity_distance': 1.0 - confidence,
                            'similarity_score': confidence,
                            'price': None,  # Price not available in autocomplete data
                            'price_currency': 'USD'
                        },
                        'match_confidence': confidence,
                        'similarity_distance': 1.0 - confidence
                    })

            return similar_products[:max_results]

        except Exception as e:
            logger.error(f"Error finding similar products for {item.product_name}: {e}")
            return []
    
    def _combine_alternatives(
        self, 
        mpn_matches: List[Dict[str, Any]], 
        similar_products: List[Dict[str, Any]],
        original_item: WishlistItem
    ) -> List[Dict[str, Any]]:
        """Combine and deduplicate alternatives."""
        
        alternatives = []
        seen_products = set()
        
        # Add MPN matches first (highest priority)
        for match in mpn_matches:
            product = match['product']
            product_key = (product['mpn'], product['brand'], product['seller'])
            
            # Skip the original item
            if (product['mpn'] == original_item.mpn and 
                product['brand'] == original_item.brand and
                product.get('seller') == original_item.seller):
                continue
            
            if product_key not in seen_products:
                alternatives.append({
                    'type': match['type'],
                    'product_id': product.get('product_id', ''),
                    'mpn': product['mpn'],
                    'name': product['name'],
                    'brand': product['brand'],
                    'category': product.get('category', ''),
                    'subcategory': product.get('subcategory', ''),
                    'seller': product.get('seller', ''),
                    'price': product.get('price'),
                    'price_currency': product.get('price_currency', 'USD'),
                    'product_url': product.get('product_url', ''),
                    'match_confidence': match['match_confidence'],
                    'similarity_distance': product.get('similarity_distance', 0.0),
                    'brand_similarity': match.get('brand_similarity', 0.0)
                })
                seen_products.add(product_key)
        
        # Add similar products
        for similar in similar_products:
            product = similar['product']
            product_key = (product['mpn'], product['brand'], product['seller'])
            
            if product_key not in seen_products:
                alternatives.append({
                    'type': similar['type'],
                    'product_id': product.get('product_id', ''),
                    'mpn': product['mpn'],
                    'name': product['name'],
                    'brand': product['brand'],
                    'category': product.get('category', ''),
                    'subcategory': product.get('subcategory', ''),
                    'seller': product.get('seller', ''),
                    'price': product.get('price'),
                    'price_currency': product.get('price_currency', 'USD'),
                    'product_url': product.get('product_url', ''),
                    'match_confidence': similar['match_confidence'],
                    'similarity_distance': similar.get('similarity_distance', 0.0),
                    'brand_similarity': 0.0
                })
                seen_products.add(product_key)
        
        # Sort by match confidence and type priority
        def sort_key(alt):
            type_priority = {'mpn_match': 3, 'semantic_similar': 2}
            return (type_priority.get(alt['type'], 1), alt['match_confidence'])
        
        alternatives.sort(key=sort_key, reverse=True)
        
        return alternatives
    
    def _analyze_prices(
        self, 
        alternatives: List[Dict[str, Any]], 
        original_item: WishlistItem
    ) -> Optional[Dict[str, Any]]:
        """Analyze price distribution and statistics."""
        
        # Get prices from alternatives
        prices = []
        price_by_seller = defaultdict(list)
        price_by_type = defaultdict(list)
        
        for alt in alternatives:
            if alt.get('price') is not None:
                price = float(alt['price'])
                prices.append(price)
                price_by_seller[alt['seller']].append(price)
                price_by_type[alt['type']].append(price)
        
        if not prices:
            return None
        
        # Calculate statistics
        analysis = {
            'total_alternatives_with_prices': len(prices),
            'price_range': {
                'min': min(prices),
                'max': max(prices),
                'median': statistics.median(prices),
                'mean': statistics.mean(prices)
            },
            'original_item_price': original_item.price,
            'potential_savings': None,
            'price_by_seller': dict(price_by_seller),
            'price_by_type': dict(price_by_type)
        }
        
        # Calculate potential savings
        if original_item.price:
            min_alternative_price = min(prices)
            if min_alternative_price < original_item.price:
                analysis['potential_savings'] = {
                    'amount': original_item.price - min_alternative_price,
                    'percentage': ((original_item.price - min_alternative_price) / original_item.price) * 100
                }
        
        return analysis
    
    def _find_best_alternative(
        self, 
        alternatives: List[Dict[str, Any]], 
        original_item: WishlistItem
    ) -> Optional[Dict[str, Any]]:
        """Find the best alternative based on price and match confidence."""
        
        if not alternatives:
            return None
        
        # Filter alternatives with prices
        priced_alternatives = [alt for alt in alternatives if alt.get('price') is not None]
        
        if not priced_alternatives:
            # Return best match by confidence if no prices available
            return max(alternatives, key=lambda x: x['match_confidence'])
        
        # Score alternatives based on price and match confidence
        def score_alternative(alt):
            price_score = 0.0
            confidence_score = alt['match_confidence']
            
            # Price scoring (lower price = higher score)
            if original_item.price and alt['price']:
                if alt['price'] < original_item.price:
                    # Savings as percentage of original price
                    price_score = ((original_item.price - alt['price']) / original_item.price) * 100
                else:
                    # Penalty for higher price
                    price_score = -((alt['price'] - original_item.price) / original_item.price) * 50
            
            # Combine scores (weight price more heavily)
            total_score = (price_score * 0.7) + (confidence_score * 30)  # confidence is 0-1, so multiply by 30
            
            return total_score
        
        best_alternative = max(priced_alternatives, key=score_alternative)
        
        # Add score to the result
        best_alternative['recommendation_score'] = score_alternative(best_alternative)
        
        return best_alternative
    
    def _generate_comparison_summary(
        self, 
        comparison_results: List[Dict[str, Any]], 
        total_savings: float
    ) -> Dict[str, Any]:
        """Generate overall comparison summary."""
        
        total_items = len(comparison_results)
        items_with_alternatives = sum(1 for r in comparison_results if r.get('alternatives'))
        items_with_savings = sum(1 for r in comparison_results 
                               if r.get('price_analysis', {}).get('potential_savings'))
        
        # Aggregate statistics
        all_alternatives = []
        for result in comparison_results:
            all_alternatives.extend(result.get('alternatives', []))
        
        sellers = set(alt['seller'] for alt in all_alternatives if alt.get('seller'))
        brands = set(alt['brand'] for alt in all_alternatives if alt.get('brand'))
        
        return {
            'total_items_compared': total_items,
            'items_with_alternatives': items_with_alternatives,
            'items_with_potential_savings': items_with_savings,
            'total_alternatives_found': len(all_alternatives),
            'unique_sellers': len(sellers),
            'unique_brands': len(brands),
            'potential_total_savings': round(total_savings, 2),
            'average_savings_per_item': round(total_savings / max(items_with_savings, 1), 2)
        }
    
    def _item_to_dict(self, item: WishlistItem) -> Dict[str, Any]:
        """Convert wishlist item to dictionary."""
        return {
            'id': str(item.id),
            'mpn': item.mpn or '',
            'name': item.product_name or '',
            'brand': item.brand or '',
            'category': item.category or '',
            'subcategory': item.subcategory or '',
            'seller': item.seller or '',
            'price': item.price,
            'price_currency': item.price_currency or 'USD',
            'product_url': item.product_url or ''
        }
