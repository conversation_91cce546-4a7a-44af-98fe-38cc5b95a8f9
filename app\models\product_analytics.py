"""
Product Analytics Model
======================

Model for tracking product interaction analytics and popularity metrics.
"""

from sqlalchemy import Column, String, DateTime, Integer, Float, Text, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
import uuid

from app.database import Base


class ProductAnalytics(Base):
    """
    Product analytics model for tracking product popularity and interactions.
    
    This model aggregates data about how often products are searched for,
    viewed, and added to wishlists. This data helps identify popular products
    and improve search rankings.
    """
    
    __tablename__ = "product_analytics"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Product identification
    product_id = Column(String(255), nullable=False, unique=True, index=True)  # ChromaDB document ID
    mpn = Column(String(255), nullable=False, index=True)  # Manufacturer Part Number
    
    # Product details (cached for analytics)
    product_name = Column(Text, nullable=False)
    brand = Column(String(255), nullable=False, index=True)
    category = Column(String(255), index=True)
    subcategory = Column(String(255))
    seller = Column(String(255), index=True)
    
    # Interaction counters
    search_appearances = Column(Integer, default=0)  # Times appeared in search results
    search_clicks = Column(Integer, default=0)  # Times clicked in search results
    wishlist_additions = Column(Integer, default=0)  # Times added to wishlist
    price_comparisons = Column(Integer, default=0)  # Times included in price comparison
    
    # Search ranking metrics
    average_search_position = Column(Float)  # Average position in search results
    average_similarity_score = Column(Float)  # Average similarity score when found
    
    # Popularity metrics
    popularity_score = Column(Float, default=0.0)  # Calculated popularity score
    trending_score = Column(Float, default=0.0)  # Recent activity score
    
    # Time-based metrics
    first_seen = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    last_interaction = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Weekly/monthly aggregations
    weekly_interactions = Column(JSONB, default=dict)  # Week-by-week interaction counts
    monthly_interactions = Column(JSONB, default=dict)  # Month-by-month interaction counts
    
    # Search query associations
    common_search_terms = Column(JSONB, default=list)  # Most common search terms that find this product
    related_products = Column(JSONB, default=list)  # Products often found together
    
    # Price tracking
    price_history = Column(JSONB, default=list)  # Historical price data points
    current_price = Column(Float)
    price_currency = Column(String(10), default="USD")
    
    # Additional metadata
    metadata = Column(JSONB, default=dict)
    
    # Indexes for analytics queries
    __table_args__ = (
        Index('idx_analytics_popularity', 'popularity_score'),
        Index('idx_analytics_trending', 'trending_score'),
        Index('idx_analytics_brand_category', 'brand', 'category'),
        Index('idx_analytics_interactions', 'search_clicks', 'wishlist_additions'),
        Index('idx_analytics_last_interaction', 'last_interaction'),
    )
    
    def __repr__(self):
        return f"<ProductAnalytics(mpn={self.mpn}, product='{self.product_name[:50]}...', popularity={self.popularity_score})>"
    
    def calculate_popularity_score(self):
        """Calculate popularity score based on interactions."""
        # Simple popularity algorithm - can be enhanced
        clicks_weight = 3.0
        wishlist_weight = 5.0
        comparison_weight = 2.0
        recency_weight = 1.0
        
        # Base score from interactions
        base_score = (
            self.search_clicks * clicks_weight +
            self.wishlist_additions * wishlist_weight +
            self.price_comparisons * comparison_weight
        )
        
        # Apply recency factor (more recent interactions are weighted higher)
        if self.last_interaction:
            days_since_interaction = (func.now() - self.last_interaction).days
            recency_factor = max(0.1, 1.0 - (days_since_interaction / 365.0))  # Decay over a year
            base_score *= recency_factor
        
        return round(base_score, 2)
    
    def to_dict(self):
        """Convert product analytics to dictionary."""
        return {
            "id": str(self.id),
            "product_id": self.product_id,
            "mpn": self.mpn,
            "product_name": self.product_name,
            "brand": self.brand,
            "category": self.category,
            "subcategory": self.subcategory,
            "seller": self.seller,
            "search_appearances": self.search_appearances,
            "search_clicks": self.search_clicks,
            "wishlist_additions": self.wishlist_additions,
            "price_comparisons": self.price_comparisons,
            "average_search_position": self.average_search_position,
            "average_similarity_score": self.average_similarity_score,
            "popularity_score": self.popularity_score,
            "trending_score": self.trending_score,
            "first_seen": self.first_seen.isoformat() if self.first_seen else None,
            "last_interaction": self.last_interaction.isoformat() if self.last_interaction else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "weekly_interactions": self.weekly_interactions,
            "monthly_interactions": self.monthly_interactions,
            "common_search_terms": self.common_search_terms,
            "related_products": self.related_products,
            "price_history": self.price_history,
            "current_price": self.current_price,
            "price_currency": self.price_currency,
            "metadata": self.metadata,
        }
