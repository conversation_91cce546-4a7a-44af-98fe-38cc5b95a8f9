#!/usr/bin/env python3
"""
Advanced Product Search and Similarity Detection
===============================================

This script implements advanced search functionality including:
1. Exact MPN matching with brand similarity
2. Cross-brand product similarity detection
3. Semantic similarity clustering
"""

import logging
from typing import List, Dict, Any, Tuple, Optional
import chromadb
from sentence_transformers import SentenceTransformer
import json
from collections import defaultdict
import numpy as np

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedProductSearcher:
    """Advanced search functionality for product embeddings database."""
    
    def __init__(self, model_name: str = "BAAI/bge-large-en-v1.5", db_path: str = "./chroma_db"):
        """
        Initialize the advanced searcher.
        
        Args:
            model_name: Name of the sentence transformer model (must match indexing model)
            db_path: Path to ChromaDB data
        """
        self.model_name = model_name
        self.db_path = db_path
        self.embedding_model = None
        self.chroma_client = None
        self.collection = None
        
    def load_embedding_model(self):
        """Load the sentence transformer model for query embeddings."""
        logger.info(f"Loading embedding model: {self.model_name}")
        self.embedding_model = SentenceTransformer(self.model_name)
        logger.info("Embedding model loaded successfully")
        
    def connect_to_database(self, collection_name: str = "product_embeddings"):
        """Connect to the ChromaDB database and collection."""
        logger.info(f"Connecting to ChromaDB at {self.db_path}")
        self.chroma_client = chromadb.PersistentClient(path=self.db_path)
        
        try:
            self.collection = self.chroma_client.get_collection(name=collection_name)
            logger.info(f"Connected to collection: {collection_name}")
            logger.info(f"Collection contains {self.collection.count()} documents")
        except Exception as e:
            logger.error(f"Failed to connect to collection {collection_name}: {e}")
            raise
    
    def exact_mpn_search_with_brand_similarity(self, mpn: str) -> Dict[str, Any]:
        """
        Search for exact MPN matches and rank by brand similarity.
        
        Args:
            mpn: Manufacturer Part Number to search for
            
        Returns:
            Dictionary containing grouped and ranked results
        """
        logger.info(f"🔍 Searching for exact MPN: '{mpn}' with brand similarity ranking")
        
        if not self.collection:
            raise ValueError("Database not connected. Call connect_to_database() first.")
        
        # Generate embedding for the MPN query using our model
        if not self.embedding_model:
            raise ValueError("Embedding model not loaded. Call load_embedding_model() first.")

        mpn_embedding = self.embedding_model.encode([mpn])

        # Get all products with this exact MPN
        results = self.collection.query(
            query_embeddings=mpn_embedding.tolist(),  # Use embedding query
            n_results=50,  # Get more results to ensure we capture all variants
            where={"mpn": mpn},  # Exact MPN filter
            include=['documents', 'metadatas', 'distances']
        )
        
        if not results['documents'][0]:
            return {
                'mpn': mpn,
                'total_variants': 0,
                'brand_groups': {},
                'all_variants': []
            }
        
        # Group by brand and calculate brand similarities
        brand_groups = defaultdict(list)
        all_variants = []
        
        documents = results['documents'][0]
        metadatas = results['metadatas'][0]
        distances = results['distances'][0]
        
        for doc, metadata, distance in zip(documents, metadatas, distances):
            brand = metadata.get('brand', 'Unknown')
            
            variant_info = {
                'document': doc,
                'metadata': metadata,
                'similarity_distance': distance,
                'name': metadata.get('name', ''),
                'brand': brand,
                'category': metadata.get('maincat', ''),
                'subcategory': metadata.get('subcat', ''),
                'seller': metadata.get('seller', ''),
                'url': metadata.get('url', ''),
                'variant_index': metadata.get('variant_index', 0)
            }
            
            brand_groups[brand].append(variant_info)
            all_variants.append(variant_info)
        
        # Sort variants within each brand group by similarity
        for brand in brand_groups:
            brand_groups[brand].sort(key=lambda x: x['similarity_distance'])
        
        # Calculate brand similarity scores (brands with similar names get higher scores)
        brand_similarity_scores = self._calculate_brand_similarity_scores(list(brand_groups.keys()))
        
        # Sort brand groups by similarity scores
        sorted_brand_groups = dict(sorted(
            brand_groups.items(),
            key=lambda x: brand_similarity_scores.get(x[0], 0),
            reverse=True
        ))
        
        return {
            'mpn': mpn,
            'total_variants': len(all_variants),
            'brand_groups': sorted_brand_groups,
            'brand_similarity_scores': brand_similarity_scores,
            'all_variants': sorted(all_variants, key=lambda x: x['similarity_distance'])
        }
    
    def _calculate_brand_similarity_scores(self, brands: List[str]) -> Dict[str, float]:
        """Calculate similarity scores between brands."""
        if not brands or not self.embedding_model:
            return {}
        
        # Generate embeddings for all brands
        brand_embeddings = self.embedding_model.encode(brands)
        
        # Calculate pairwise similarities and create scores
        similarity_scores = {}
        
        for i, brand in enumerate(brands):
            # Calculate average similarity to all other brands
            similarities = []
            for j, other_brand in enumerate(brands):
                if i != j:
                    # Cosine similarity
                    similarity = np.dot(brand_embeddings[i], brand_embeddings[j]) / (
                        np.linalg.norm(brand_embeddings[i]) * np.linalg.norm(brand_embeddings[j])
                    )
                    similarities.append(similarity)
            
            # Average similarity score (higher = more similar to other brands)
            similarity_scores[brand] = np.mean(similarities) if similarities else 0.0
        
        return similarity_scores
    
    def cross_brand_similarity_search(self, query: str, similarity_threshold: float = 0.3, 
                                    max_results: int = 50) -> Dict[str, Any]:
        """
        Find products that represent the same item across different brands.
        
        Args:
            query: Product description or name to search for
            similarity_threshold: Distance threshold for high-confidence matches
            max_results: Maximum number of results to return
            
        Returns:
            Dictionary containing similarity clusters and analysis
        """
        logger.info(f"🔍 Cross-brand similarity search for: '{query}' (threshold: {similarity_threshold})")
        
        if not self.embedding_model or not self.collection:
            raise ValueError("Model and database must be loaded. Call load_embedding_model() and connect_to_database() first.")
        
        # Generate embedding for the query
        query_embedding = self.embedding_model.encode([query])

        # Perform semantic search
        results = self.collection.query(
            query_embeddings=query_embedding.tolist(),
            n_results=max_results,
            include=['documents', 'metadatas', 'distances']
        )
        
        if not results['documents'][0]:
            return {
                'query': query,
                'high_confidence_matches': [],
                'similarity_clusters': {},
                'brand_distribution': {}
            }
        
        documents = results['documents'][0]
        metadatas = results['metadatas'][0]
        distances = results['distances'][0]
        
        # Filter high-confidence matches
        high_confidence_matches = []
        all_matches = []
        
        for doc, metadata, distance in zip(documents, metadatas, distances):
            match_info = {
                'document': doc,
                'metadata': metadata,
                'similarity_distance': distance,
                'similarity_score': 1 - distance,  # Convert distance to similarity score
                'name': metadata.get('name', ''),
                'brand': metadata.get('brand', ''),
                'mpn': metadata.get('mpn', ''),
                'category': metadata.get('maincat', ''),
                'subcategory': metadata.get('subcat', ''),
                'seller': metadata.get('seller', ''),
                'url': metadata.get('url', '')
            }
            
            all_matches.append(match_info)
            
            if distance <= similarity_threshold:
                high_confidence_matches.append(match_info)
        
        # Create similarity clusters
        similarity_clusters = self._create_similarity_clusters(all_matches, similarity_threshold)
        
        # Analyze brand distribution
        brand_distribution = self._analyze_brand_distribution(all_matches)
        
        return {
            'query': query,
            'total_matches': len(all_matches),
            'high_confidence_matches': high_confidence_matches,
            'similarity_clusters': similarity_clusters,
            'brand_distribution': brand_distribution,
            'all_matches': all_matches
        }
    
    def _create_similarity_clusters(self, matches: List[Dict], threshold: float) -> Dict[str, List[Dict]]:
        """Group similar products into clusters."""
        clusters = {}
        cluster_id = 0
        
        # Group by similarity ranges
        very_high_sim = [m for m in matches if m['similarity_distance'] <= 0.1]
        high_sim = [m for m in matches if 0.1 < m['similarity_distance'] <= threshold]
        medium_sim = [m for m in matches if threshold < m['similarity_distance'] <= 0.5]
        low_sim = [m for m in matches if m['similarity_distance'] > 0.5]
        
        if very_high_sim:
            clusters['very_high_similarity'] = very_high_sim
        if high_sim:
            clusters['high_similarity'] = high_sim
        if medium_sim:
            clusters['medium_similarity'] = medium_sim
        if low_sim:
            clusters['low_similarity'] = low_sim
        
        return clusters
    
    def _analyze_brand_distribution(self, matches: List[Dict]) -> Dict[str, Any]:
        """Analyze the distribution of brands in search results."""
        brand_counts = defaultdict(int)
        brand_avg_similarity = defaultdict(list)
        
        for match in matches:
            brand = match['brand']
            brand_counts[brand] += 1
            brand_avg_similarity[brand].append(match['similarity_score'])
        
        # Calculate average similarity per brand
        brand_stats = {}
        for brand, similarities in brand_avg_similarity.items():
            brand_stats[brand] = {
                'count': brand_counts[brand],
                'avg_similarity': np.mean(similarities),
                'max_similarity': max(similarities),
                'min_similarity': min(similarities)
            }
        
        return {
            'total_brands': len(brand_counts),
            'brand_stats': dict(sorted(brand_stats.items(), key=lambda x: x[1]['avg_similarity'], reverse=True))
        }
    
    def format_mpn_search_results(self, results: Dict[str, Any]) -> str:
        """Format MPN search results for display."""
        if results['total_variants'] == 0:
            return f"❌ No products found for MPN: {results['mpn']}"
        
        output = []
        output.append(f"🔍 MPN SEARCH RESULTS FOR: {results['mpn']}")
        output.append(f"📊 Total Variants Found: {results['total_variants']}")
        output.append("=" * 80)
        
        for brand, variants in results['brand_groups'].items():
            brand_score = results['brand_similarity_scores'].get(brand, 0)
            output.append(f"\n🏷️  BRAND: {brand} (Similarity Score: {brand_score:.3f})")
            output.append("-" * 60)
            
            for i, variant in enumerate(variants, 1):
                output.append(f"  {i}. {variant['name']}")
                output.append(f"     Category: {variant['category']} > {variant['subcategory']}")
                output.append(f"     Seller: {variant['seller']}")
                output.append(f"     Similarity Distance: {variant['similarity_distance']:.4f}")
                if variant['url']:
                    output.append(f"     URL: {variant['url']}")
                output.append("")
        
        return "\n".join(output)
    
    def format_cross_brand_results(self, results: Dict[str, Any]) -> str:
        """Format cross-brand similarity results for display."""
        output = []
        output.append(f"🔍 CROSS-BRAND SIMILARITY SEARCH FOR: '{results['query']}'")
        output.append(f"📊 Total Matches: {results['total_matches']}")
        output.append(f"🎯 High-Confidence Matches: {len(results['high_confidence_matches'])}")
        output.append("=" * 80)
        
        # Show high-confidence matches first
        if results['high_confidence_matches']:
            output.append("\n🎯 HIGH-CONFIDENCE MATCHES (Likely Same Product)")
            output.append("-" * 60)
            
            for i, match in enumerate(results['high_confidence_matches'], 1):
                output.append(f"  {i}. {match['name']}")
                output.append(f"     Brand: {match['brand']} | MPN: {match['mpn']}")
                output.append(f"     Category: {match['category']} > {match['subcategory']}")
                output.append(f"     Seller: {match['seller']}")
                output.append(f"     Similarity Score: {match['similarity_score']:.4f} (Distance: {match['similarity_distance']:.4f})")
                output.append("")
        
        # Show similarity clusters
        output.append("\n📊 SIMILARITY CLUSTERS")
        output.append("-" * 60)
        
        for cluster_name, cluster_matches in results['similarity_clusters'].items():
            output.append(f"\n{cluster_name.upper().replace('_', ' ')} ({len(cluster_matches)} products):")
            
            for match in cluster_matches[:5]:  # Show top 5 in each cluster
                output.append(f"  • {match['name']} | {match['brand']} | Score: {match['similarity_score']:.3f}")
        
        # Show brand distribution
        output.append(f"\n🏷️  BRAND DISTRIBUTION ({results['brand_distribution']['total_brands']} brands)")
        output.append("-" * 60)
        
        for brand, stats in list(results['brand_distribution']['brand_stats'].items())[:10]:
            output.append(f"  {brand}: {stats['count']} products, Avg Similarity: {stats['avg_similarity']:.3f}")
        
        return "\n".join(output)


def run_comprehensive_tests():
    """Run comprehensive tests for the advanced search functionality."""

    # Initialize searcher
    searcher = AdvancedProductSearcher()

    try:
        # Load model and connect to database
        searcher.load_embedding_model()
        searcher.connect_to_database()

        print("🚀 ADVANCED PRODUCT SEARCH TESTING")
        print("=" * 80)

        # Test 1: Exact MPN matching with brand similarity
        print("\n" + "="*80)
        print("TEST 1: EXACT MPN MATCHING WITH BRAND SIMILARITY")
        print("="*80)

        test_mpn = "6010002"
        mpn_results = searcher.exact_mpn_search_with_brand_similarity(test_mpn)
        formatted_mpn = searcher.format_mpn_search_results(mpn_results)
        print(formatted_mpn)

        # Test 2: Cross-brand similarity detection
        print("\n" + "="*80)
        print("TEST 2: CROSS-BRAND PRODUCT SIMILARITY DETECTION")
        print("="*80)

        test_queries = [
            "Millennium Heat Cure",
            "dental acrylic resin",
            "orthodontic retainer material",
            "denture repair"
        ]

        for query in test_queries:
            print(f"\n{'='*60}")
            print(f"QUERY: {query}")
            print('='*60)

            cross_brand_results = searcher.cross_brand_similarity_search(
                query,
                similarity_threshold=0.3,
                max_results=20
            )
            formatted_cross = searcher.format_cross_brand_results(cross_brand_results)
            print(formatted_cross)

        # Test 3: Additional MPN tests
        print("\n" + "="*80)
        print("TEST 3: ADDITIONAL MPN TESTS")
        print("="*80)

        additional_mpns = ["6010049", "1020-WHITE", "UBAR11AL"]

        for mpn in additional_mpns:
            print(f"\n{'-'*60}")
            print(f"MPN: {mpn}")
            print('-'*60)

            mpn_results = searcher.exact_mpn_search_with_brand_similarity(mpn)
            formatted_mpn = searcher.format_mpn_search_results(mpn_results)
            print(formatted_mpn)

        print("\n🎉 COMPREHENSIVE TESTING COMPLETED!")

    except Exception as e:
        logger.error(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_comprehensive_tests()
