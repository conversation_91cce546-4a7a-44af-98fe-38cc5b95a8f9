/* Product Search & Price Comparison - Main Styles */

:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --error-color: #dc2626;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --border-color: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON>l, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header */
.header {
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    transition: color 0.2s;
}

.nav-link:hover {
    color: var(--primary-color);
}

/* Main Content */
.main {
    padding: 2rem 0;
}

.section {
    margin-bottom: 3rem;
}

/* Search Section */
.search-container {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-md);
}

.search-container h2 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.search-box {
    position: relative;
    margin-bottom: 2rem;
}

.search-input {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    box-shadow: var(--shadow-lg);
    max-height: 300px;
    overflow-y: auto;
    z-index: 50;
    display: none;
}

.search-suggestions.show {
    display: block;
}

.suggestion-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s, transform 0.1s;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.suggestion-item:hover {
    background-color: var(--background-color);
    transform: translateX(2px);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    flex: 1;
    cursor: pointer;
}

.suggestion-actions {
    margin-left: 1rem;
    display: flex;
    align-items: center;
}

.suggestion-text {
    font-weight: 500;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.suggestion-text strong {
    background-color: var(--primary-color);
    color: white;
    padding: 0.1rem 0.2rem;
    border-radius: 0.2rem;
    font-weight: 600;
}

.suggestion-meta {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-left: 1.5rem;
}

/* Match type specific styling */
.suggestion-mpn_exact {
    border-left: 3px solid var(--success-color);
    background-color: rgba(5, 150, 105, 0.05);
}

.suggestion-name_prefix {
    border-left: 3px solid var(--primary-color);
}

.suggestion-name_contains {
    border-left: 3px solid var(--warning-color);
}

.suggestion-brand {
    border-left: 3px solid var(--secondary-color);
}

.suggestion-popular {
    border-left: 3px solid #f59e0b;
    background-color: rgba(245, 158, 11, 0.05);
}

/* Search Results */
.search-results {
    display: none;
}

.search-results.show {
    display: block;
}

.results-section {
    margin-bottom: 2rem;
}

.results-section h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.product-card {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    transition: box-shadow 0.2s, transform 0.2s;
    cursor: pointer;
}

.product-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.product-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.product-meta {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.product-brand, .product-mpn, .product-category {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.product-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.similarity-score {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #475569;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.btn-outline:hover {
    background: var(--background-color);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
    border-radius: 0.25rem;
}

/* Wishlist Section */
.wishlist-container {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-md);
}

.wishlist-items {
    margin-bottom: 2rem;
}

.wishlist-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    background: var(--surface-color);
}

.wishlist-item-info {
    flex: 1;
}

.wishlist-item-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.wishlist-item-meta {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.wishlist-item-actions {
    display: flex;
    gap: 0.5rem;
}

.wishlist-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Price Comparison Section */
.comparison-container {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-md);
}

.comparison-results {
    display: none;
}

.comparison-results.show {
    display: block;
}

.comparison-item {
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.comparison-header {
    background: var(--background-color);
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.comparison-alternatives {
    padding: 1rem;
}

.alternative-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    transition: background-color 0.2s;
}

.alternative-card:hover {
    background: var(--background-color);
}

.alternative-info {
    flex: 1;
}

.alternative-price {
    font-weight: 600;
    color: var(--success-color);
    font-size: 1.125rem;
}

.savings-badge {
    background: var(--success-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    display: none;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay p {
    color: white;
    font-weight: 500;
}

/* Footer */
.footer {
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
    padding: 2rem 0;
    margin-top: 4rem;
    text-align: center;
    color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 0.5rem;
    }
    
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav {
        gap: 1rem;
    }
    
    .results-grid {
        grid-template-columns: 1fr;
    }
    
    .wishlist-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .wishlist-item-actions {
        align-self: flex-end;
    }
    
    .alternative-card {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .wishlist-actions {
        flex-direction: column;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-success {
    color: var(--success-color);
}

.text-error {
    color: var(--error-color);
}

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
