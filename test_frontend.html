<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend Functionality</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>Frontend Functionality Test</h1>
    
    <div class="test-section">
        <h2>1. Test Autocomplete API</h2>
        <button onclick="testAutocomplete()">Test Autocomplete</button>
        <div id="autocomplete-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Test Session Creation</h2>
        <button onclick="testSession()">Create Session</button>
        <div id="session-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Test Add to Wishlist</h2>
        <button onclick="testAddToWishlist()">Add Product to Wishlist</button>
        <div id="wishlist-add-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Test Load Wishlist</h2>
        <button onclick="testLoadWishlist()">Load Wishlist</button>
        <div id="wishlist-load-result"></div>
    </div>

    <script>
        let sessionToken = null;

        async function testAutocomplete() {
            const resultDiv = document.getElementById('autocomplete-result');
            try {
                const response = await fetch('http://127.0.0.1:8001/api/autocomplete/suggestions?q=dental&limit=3');
                const data = await response.json();
                
                if (data.status === 'success') {
                    resultDiv.innerHTML = `<div class="success">✅ Autocomplete working! Found ${data.suggestions.length} suggestions</div>
                        <pre>${JSON.stringify(data.suggestions[0], null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Autocomplete failed: ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function testSession() {
            const resultDiv = document.getElementById('session-result');
            try {
                const response = await fetch('http://127.0.0.1:8001/api/session/create', {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.session_token) {
                    sessionToken = data.session_token;
                    resultDiv.innerHTML = `<div class="success">✅ Session created! Token: ${sessionToken.substring(0, 8)}...</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Session creation failed</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function testAddToWishlist() {
            const resultDiv = document.getElementById('wishlist-add-result');
            
            if (!sessionToken) {
                resultDiv.innerHTML = `<div class="error">❌ Please create a session first</div>`;
                return;
            }

            try {
                const response = await fetch('http://127.0.0.1:8001/api/wishlist/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Session-Token': sessionToken
                    },
                    body: JSON.stringify({
                        product_id: "test-123",
                        mpn: "TEST-MPN",
                        product_name: "Test Product",
                        brand: "Test Brand",
                        category: "Test Category",
                        seller: "Test Seller",
                        product_url: "https://example.com"
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">✅ Product added to wishlist! ID: ${data.item_id}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Failed to add: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function testLoadWishlist() {
            const resultDiv = document.getElementById('wishlist-load-result');
            
            if (!sessionToken) {
                resultDiv.innerHTML = `<div class="error">❌ Please create a session first</div>`;
                return;
            }

            try {
                const response = await fetch('http://127.0.0.1:8001/api/wishlist/', {
                    headers: {
                        'X-Session-Token': sessionToken
                    }
                });
                
                const data = await response.json();
                
                if (data.items) {
                    resultDiv.innerHTML = `<div class="success">✅ Wishlist loaded! ${data.total_items} items found</div>
                        <pre>${JSON.stringify(data.items, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Failed to load wishlist</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
