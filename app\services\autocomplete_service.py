"""
Fast Autocomplete Service
========================

High-performance autocomplete service for instant product suggestions.
"""

import asyncio
import logging
import json
import re
from typing import List, Dict, Any, Optional
from collections import defaultdict
import chromadb
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


class FastAutocompleteService:
    """
    Fast autocomplete service using in-memory indices.
    
    Provides sub-100ms response times for product suggestions.
    """
    
    def __init__(self, db_path: str = "./chroma_db", collection_name: str = "product_embeddings"):
        """Initialize the autocomplete service."""
        self.db_path = db_path
        self.collection_name = collection_name
        self.is_initialized = False
        
        # In-memory indices for fast searching
        self.name_index = defaultdict(list)      # name -> products
        self.mpn_index = defaultdict(list)       # mpn -> products
        self.brand_index = defaultdict(list)     # brand -> products
        self.word_index = defaultdict(list)      # word -> products
        self.popular_products = []               # most common products
        
        self.executor = ThreadPoolExecutor(max_workers=2)
    
    async def initialize(self):
        """Initialize the autocomplete service by loading data."""
        if self.is_initialized:
            return
        
        logger.info("🚀 Initializing fast autocomplete service...")
        
        # Load data in background thread
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(self.executor, self._load_autocomplete_data)
        
        self.is_initialized = True
        logger.info("✅ Fast autocomplete service initialized")
    
    def _load_autocomplete_data(self):
        """Load and index product data for autocomplete."""
        logger.info("Loading product data from ChromaDB...")
        
        # Connect to ChromaDB
        client = chromadb.PersistentClient(path=self.db_path)
        collection = client.get_collection(self.collection_name)
        
        # Get all products in batches
        batch_size = 10000
        total_count = collection.count()
        processed = 0
        
        products_by_popularity = defaultdict(int)
        
        while processed < total_count:
            logger.info(f"Processing batch {processed//batch_size + 1}")
            
            # Get batch
            results = collection.get(
                limit=batch_size,
                offset=processed,
                include=['documents', 'metadatas']
            )
            
            # Process each product
            for i, (doc, metadata) in enumerate(zip(results['documents'], results['metadatas'])):
                try:
                    # Parse document: "name | brand | maincat | subcat"
                    parts = doc.split(' | ')
                    if len(parts) >= 3:
                        name = parts[0].strip()
                        brand = parts[1].strip()
                        category = parts[2].strip()
                        subcategory = parts[3].strip() if len(parts) > 3 else ""
                        
                        mpn = metadata.get('mpn', '').strip()
                        seller = metadata.get('seller', '').strip()
                        url = metadata.get('url', '').strip()
                        
                        if name and brand and mpn:
                            product = {
                                'id': f"{processed + i}",
                                'name': name,
                                'brand': brand,
                                'mpn': mpn,
                                'category': category,
                                'subcategory': subcategory,
                                'seller': seller,
                                'url': url
                            }
                            
                            # Index the product
                            self._index_product(product)
                            
                            # Track popularity
                            popularity_key = f"{name}|{brand}"
                            products_by_popularity[popularity_key] += 1
                
                except Exception as e:
                    logger.warning(f"Error processing product {processed + i}: {e}")
                    continue
            
            processed += batch_size
        
        # Create popular products list
        self._create_popular_products_list(products_by_popularity)
        
        logger.info(f"Indexed {len(self.name_index)} name entries")
        logger.info(f"Indexed {len(self.mpn_index)} MPN entries")
        logger.info(f"Indexed {len(self.brand_index)} brand entries")
        logger.info(f"Popular products: {len(self.popular_products)}")
    
    def _index_product(self, product: Dict[str, str]):
        """Index a single product for fast searching."""
        name = product['name'].lower()
        mpn = product['mpn'].lower()
        brand = product['brand'].lower()
        
        # Index by name prefixes (2-6 characters)
        for i in range(2, min(len(name) + 1, 7)):
            prefix = name[:i]
            self.name_index[prefix].append(product)
        
        # Index by name words
        words = re.findall(r'\b\w{2,}\b', name)  # Words with 2+ characters
        for word in words:
            self.word_index[word].append(product)
        
        # Index by MPN prefixes (2-8 characters)
        if mpn:
            for i in range(2, min(len(mpn) + 1, 9)):
                prefix = mpn[:i]
                self.mpn_index[prefix].append(product)
        
        # Index by brand prefixes (2-5 characters)
        if brand:
            for i in range(2, min(len(brand) + 1, 6)):
                prefix = brand[:i]
                self.brand_index[prefix].append(product)
    
    def _create_popular_products_list(self, popularity_data: Dict[str, int]):
        """Create list of popular products for default suggestions."""
        # Sort by popularity and get top items
        popular_items = sorted(popularity_data.items(), key=lambda x: x[1], reverse=True)[:200]
        
        # Find actual products for popular items
        popular_keys = {item[0] for item in popular_items}
        
        for products in self.name_index.values():
            for product in products:
                key = f"{product['name']}|{product['brand']}"
                if key in popular_keys and len(self.popular_products) < 100:
                    # Avoid duplicates
                    if not any(p['name'] == product['name'] and p['brand'] == product['brand'] 
                             for p in self.popular_products):
                        self.popular_products.append(product)
    
    async def get_suggestions(self, query: str, max_results: int = 8) -> List[Dict[str, Any]]:
        """
        Get autocomplete suggestions for a query.
        
        Args:
            query: Search query (2+ characters)
            max_results: Maximum number of suggestions to return
            
        Returns:
            List of product suggestions with match type
        """
        if not self.is_initialized:
            await self.initialize()
        
        if len(query.strip()) < 2:
            # Return popular products for short queries
            return [
                {
                    'text': product['name'],
                    'brand': product['brand'],
                    'mpn': product['mpn'],
                    'category': product['category'],
                    'match_type': 'popular',
                    'product_id': product['id'],
                    'url': product['url']
                }
                for product in self.popular_products[:max_results]
            ]
        
        query_lower = query.lower().strip()
        suggestions = []
        seen_products = set()
        
        # 1. Exact MPN matches (highest priority)
        mpn_matches = self.mpn_index.get(query_lower, [])
        for product in mpn_matches[:3]:  # Limit MPN matches
            key = f"{product['name']}|{product['brand']}"
            if key not in seen_products:
                suggestions.append({
                    'text': product['name'],
                    'brand': product['brand'],
                    'mpn': product['mpn'],
                    'category': product['category'],
                    'match_type': 'mpn_exact',
                    'product_id': product['id'],
                    'url': product['url']
                })
                seen_products.add(key)
        
        # 2. Name prefix matches
        if len(suggestions) < max_results:
            name_matches = self.name_index.get(query_lower, [])
            for product in name_matches:
                if len(suggestions) >= max_results:
                    break
                key = f"{product['name']}|{product['brand']}"
                if key not in seen_products:
                    suggestions.append({
                        'text': product['name'],
                        'brand': product['brand'],
                        'mpn': product['mpn'],
                        'category': product['category'],
                        'match_type': 'name_prefix',
                        'product_id': product['id'],
                        'url': product['url']
                    })
                    seen_products.add(key)
        
        # 3. Word matches in product names
        if len(suggestions) < max_results:
            word_matches = self.word_index.get(query_lower, [])
            for product in word_matches:
                if len(suggestions) >= max_results:
                    break
                key = f"{product['name']}|{product['brand']}"
                if key not in seen_products:
                    suggestions.append({
                        'text': product['name'],
                        'brand': product['brand'],
                        'mpn': product['mpn'],
                        'category': product['category'],
                        'match_type': 'name_contains',
                        'product_id': product['id'],
                        'url': product['url']
                    })
                    seen_products.add(key)
        
        # 4. Brand matches
        if len(suggestions) < max_results:
            brand_matches = self.brand_index.get(query_lower, [])
            for product in brand_matches:
                if len(suggestions) >= max_results:
                    break
                key = f"{product['name']}|{product['brand']}"
                if key not in seen_products:
                    suggestions.append({
                        'text': product['name'],
                        'brand': product['brand'],
                        'mpn': product['mpn'],
                        'category': product['category'],
                        'match_type': 'brand',
                        'product_id': product['id'],
                        'url': product['url']
                    })
                    seen_products.add(key)
        
        # 5. Fuzzy matching for partial queries
        if len(suggestions) < max_results and len(query_lower) >= 3:
            # Look for partial matches in longer prefixes
            for prefix_len in range(len(query_lower) - 1, 1, -1):
                partial_query = query_lower[:prefix_len]
                partial_matches = self.name_index.get(partial_query, [])
                
                for product in partial_matches:
                    if len(suggestions) >= max_results:
                        break
                    key = f"{product['name']}|{product['brand']}"
                    if key not in seen_products and query_lower in product['name'].lower():
                        suggestions.append({
                            'text': product['name'],
                            'brand': product['brand'],
                            'mpn': product['mpn'],
                            'category': product['category'],
                            'match_type': 'fuzzy',
                            'product_id': product['id'],
                            'url': product['url']
                        })
                        seen_products.add(key)
                
                if len(suggestions) >= max_results:
                    break
        
        return suggestions[:max_results]
    
    async def close(self):
        """Close the autocomplete service."""
        if self.executor:
            self.executor.shutdown(wait=True)
        logger.info("Autocomplete service closed")


# Global instance
_autocomplete_service = None


async def get_autocomplete_service() -> FastAutocompleteService:
    """Get the global autocomplete service instance."""
    global _autocomplete_service
    if _autocomplete_service is None:
        _autocomplete_service = FastAutocompleteService()
        await _autocomplete_service.initialize()
    return _autocomplete_service
