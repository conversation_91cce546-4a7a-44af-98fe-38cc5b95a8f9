#!/usr/bin/env python3
"""
Database Setup Script
====================

Script to set up PostgreSQL database and run migrations.
"""

import asyncio
import logging
import sys
import subprocess
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent))

from app.config import get_settings
from app.database import init_db, async_engine

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

settings = get_settings()


async def check_database_connection():
    """Check if we can connect to the database."""
    try:
        from sqlalchemy import text
        async with async_engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            logger.info("✅ Database connection successful")
            return True
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False


def run_alembic_migrations():
    """Run Alembic migrations to create database schema."""
    try:
        logger.info("Running Alembic migrations...")

        # Initialize Alembic if not already done
        try:
            subprocess.run([sys.executable, "-m", "alembic", "revision", "--autogenerate", "-m", "Initial migration"],
                         check=True, capture_output=True)
            logger.info("Created initial migration")
        except subprocess.CalledProcessError as e:
            if "already exists" not in str(e.stderr):
                logger.warning(f"Migration creation warning: {e}")

        # Run migrations
        result = subprocess.run([sys.executable, "-m", "alembic", "upgrade", "head"],
                              check=True, capture_output=True, text=True)
        logger.info("✅ Alembic migrations completed successfully")
        return True

    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Alembic migration failed: {e}")
        logger.error(f"stdout: {e.stdout}")
        logger.error(f"stderr: {e.stderr}")
        return False
    except FileNotFoundError:
        logger.error("❌ Alembic not found. Please install it: pip install alembic")
        return False


async def create_sample_data():
    """Create some sample data for testing."""
    try:
        from app.models.user_session import UserSession
        from app.database import AsyncSessionLocal
        from datetime import datetime, timedelta
        import uuid
        
        async with AsyncSessionLocal() as session:
            # Create a sample session
            sample_session = UserSession(
                session_token=str(uuid.uuid4()),
                expires_at=datetime.utcnow() + timedelta(hours=24),
                is_active=True,
                user_agent="Sample User Agent",
                ip_address="127.0.0.1"
            )
            
            session.add(sample_session)
            await session.commit()
            
            logger.info("✅ Sample data created successfully")
            
    except Exception as e:
        logger.error(f"❌ Failed to create sample data: {e}")


def print_database_info():
    """Print database configuration information."""
    logger.info("📊 Database Configuration:")
    logger.info(f"  Host: {settings.postgres_host}")
    logger.info(f"  Port: {settings.postgres_port}")
    logger.info(f"  Database: {settings.postgres_db}")
    logger.info(f"  User: {settings.postgres_user}")
    logger.info(f"  URL: {settings.postgres_sync_url}")


async def main():
    """Main setup function."""
    logger.info("🗄️  Setting up PostgreSQL database for Product Search Application")
    
    print_database_info()
    
    # Check database connection
    if not await check_database_connection():
        logger.error("Please ensure PostgreSQL is running and configured correctly")
        logger.error("You may need to:")
        logger.error("1. Install PostgreSQL")
        logger.error("2. Create the database")
        logger.error("3. Update the connection settings in .env")
        sys.exit(1)
    
    # Skip Alembic for now and create tables directly
    logger.info("Skipping Alembic migrations, creating tables directly...")
    
    # Initialize database (create tables if needed)
    try:
        await init_db()
        logger.info("✅ Database initialization completed")
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        sys.exit(1)
    
    # Create sample data
    await create_sample_data()
    
    logger.info("🎉 Database setup completed successfully!")
    logger.info("You can now run the application with: python run_app.py")


if __name__ == "__main__":
    asyncio.run(main())
