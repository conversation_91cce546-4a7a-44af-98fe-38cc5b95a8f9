"""
User Session Model
=================

Model for managing user sessions and temporary data.
"""

from sqlalchemy import Column, String, DateTime, Boolean, Text, Integer, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
import uuid

from app.database import Base


class UserSession(Base):
    """
    User session model for tracking user interactions.
    
    This model stores session data for users without requiring authentication.
    Each session is identified by a UUID and can store temporary data like
    search preferences, wishlist items, and interaction history.
    """
    
    __tablename__ = "user_sessions"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Session identification
    session_token = Column(String(255), unique=True, nullable=False, index=True)
    
    # Session metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    
    # Session status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # User agent and IP for analytics
    user_agent = Column(Text)
    ip_address = Column(String(45))  # IPv6 compatible
    
    # Session data (flexible JSON storage)
    session_data = Column(JSON, default=dict)
    
    # Search preferences
    preferred_max_results = Column(Integer, default=20)
    preferred_similarity_threshold = Column(String(10), default="0.3")
    
    # Analytics counters
    search_count = Column(Integer, default=0)
    wishlist_interactions = Column(Integer, default=0)
    price_comparisons = Column(Integer, default=0)
    
    def __repr__(self):
        return f"<UserSession(id={self.id}, token={self.session_token[:8]}..., active={self.is_active})>"
    
    def to_dict(self):
        """Convert session to dictionary."""
        return {
            "id": str(self.id),
            "session_token": self.session_token,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "is_active": self.is_active,
            "session_data": self.session_data,
            "search_count": self.search_count,
            "wishlist_interactions": self.wishlist_interactions,
            "price_comparisons": self.price_comparisons,
        }
