"""
Search API Schemas
=================

Pydantic schemas for search API requests and responses.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime


class SearchRequest(BaseModel):
    """Schema for search requests."""
    
    query: str = Field(..., min_length=1, max_length=500, description="Search query")
    max_results: Optional[int] = Field(default=20, ge=1, le=100, description="Maximum number of results")
    similarity_threshold: Optional[float] = Field(default=0.3, ge=0.0, le=1.0, description="Similarity threshold")
    search_type: Optional[str] = Field(default="semantic", description="Type of search: semantic, mpn, autocomplete")
    
    @validator('query')
    def validate_query(cls, v):
        """Validate search query."""
        if not v.strip():
            raise ValueError('Query cannot be empty')
        return v.strip()


class AutocompleteRequest(BaseModel):
    """Schema for autocomplete requests."""
    
    query: str = Field(..., min_length=1, max_length=200, description="Partial search query")
    max_suggestions: Optional[int] = Field(default=10, ge=1, le=20, description="Maximum number of suggestions")
    
    @validator('query')
    def validate_query(cls, v):
        """Validate autocomplete query."""
        if not v.strip():
            raise ValueError('Query cannot be empty')
        return v.strip()


class MPNSearchRequest(BaseModel):
    """Schema for MPN search requests."""
    
    mpn: str = Field(..., min_length=1, max_length=100, description="Manufacturer Part Number")
    
    @validator('mpn')
    def validate_mpn(cls, v):
        """Validate MPN."""
        if not v.strip():
            raise ValueError('MPN cannot be empty')
        return v.strip()


class ProductMatch(BaseModel):
    """Schema for individual product match."""
    
    product_id: str = Field(..., description="Product ID from ChromaDB")
    mpn: str = Field(..., description="Manufacturer Part Number")
    name: str = Field(..., description="Product name")
    brand: str = Field(..., description="Brand name")
    category: str = Field(..., description="Main category")
    subcategory: Optional[str] = Field(None, description="Subcategory")
    seller: str = Field(..., description="Seller name")
    price: Optional[float] = Field(None, description="Product price")
    price_currency: Optional[str] = Field(default="USD", description="Price currency")
    product_url: Optional[str] = Field(None, description="Product URL")
    similarity_score: float = Field(..., description="Similarity score (0-1)")
    similarity_distance: float = Field(..., description="Similarity distance")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")


class BrandGroup(BaseModel):
    """Schema for brand grouping in MPN search."""
    
    brand: str = Field(..., description="Brand name")
    similarity_score: float = Field(..., description="Brand similarity score")
    products: List[ProductMatch] = Field(..., description="Products from this brand")


class SearchPerformance(BaseModel):
    """Schema for search performance metrics."""
    
    search_duration_ms: float = Field(..., description="Search duration in milliseconds")
    total_results_processed: int = Field(..., description="Total results processed")
    similarity_threshold: float = Field(..., description="Similarity threshold used")
    search_type: Optional[str] = Field(None, description="Type of search performed")


class BrandDistribution(BaseModel):
    """Schema for brand distribution in search results."""
    
    total_brands: int = Field(..., description="Total number of brands found")
    brand_stats: Dict[str, Dict[str, Any]] = Field(..., description="Statistics per brand")


class SearchResponse(BaseModel):
    """Schema for search responses."""
    
    query: str = Field(..., description="Original search query")
    exact_matches: List[ProductMatch] = Field(default_factory=list, description="Exact matches")
    high_confidence_similar: List[ProductMatch] = Field(default_factory=list, description="High confidence similar products")
    medium_confidence_similar: List[ProductMatch] = Field(default_factory=list, description="Medium confidence similar products")
    category_related: List[ProductMatch] = Field(default_factory=list, description="Category related products")
    total_results: int = Field(..., description="Total number of results found")
    brand_distribution: Optional[BrandDistribution] = Field(None, description="Brand distribution")
    performance: SearchPerformance = Field(..., description="Performance metrics")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Search timestamp")


class MPNSearchResponse(BaseModel):
    """Schema for MPN search responses."""
    
    mpn: str = Field(..., description="Searched MPN")
    total_variants: int = Field(..., description="Total variants found")
    brand_groups: List[BrandGroup] = Field(default_factory=list, description="Products grouped by brand")
    brand_similarity_scores: Dict[str, float] = Field(default_factory=dict, description="Brand similarity scores")
    performance: SearchPerformance = Field(..., description="Performance metrics")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Search timestamp")


class AutocompleteSuggestion(BaseModel):
    """Schema for autocomplete suggestions."""
    
    text: str = Field(..., description="Suggestion text")
    type: str = Field(..., description="Suggestion type: exact_match, similar, category")
    brand: str = Field(..., description="Product brand")
    mpn: str = Field(..., description="Product MPN")
    category: str = Field(..., description="Product category")
    similarity_score: float = Field(..., description="Similarity score")


class AutocompleteResponse(BaseModel):
    """Schema for autocomplete responses."""
    
    query: str = Field(..., description="Original query")
    suggestions: List[AutocompleteSuggestion] = Field(default_factory=list, description="Autocomplete suggestions")
    total_suggestions: int = Field(..., description="Total number of suggestions")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")


class SearchError(BaseModel):
    """Schema for search error responses."""
    
    error: str = Field(..., description="Error message")
    error_type: str = Field(..., description="Error type")
    query: Optional[str] = Field(None, description="Original query that caused the error")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")
