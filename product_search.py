#!/usr/bin/env python3
"""
Product Search and Retrieval
============================

This script provides search and retrieval functionality for the product embeddings
stored in ChromaDB. It includes various search methods and testing functions.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
import chromadb
from sentence_transformers import SentenceTransformer
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProductSearcher:
    """Handles search and retrieval operations on the product embeddings database."""
    
    def __init__(self, model_name: str = "BAAI/bge-large-en-v1.5", db_path: str = "./chroma_db"):
        """
        Initialize the searcher with embedding model and database path.
        
        Args:
            model_name: Name of the sentence transformer model (must match the one used for indexing)
            db_path: Path to ChromaDB data
        """
        self.model_name = model_name
        self.db_path = db_path
        self.embedding_model = None
        self.chroma_client = None
        self.collection = None
        
    def load_embedding_model(self):
        """Load the sentence transformer model for query embeddings."""
        logger.info(f"Loading embedding model: {self.model_name}")
        self.embedding_model = SentenceTransformer(self.model_name)
        logger.info("Embedding model loaded successfully")
        
    def connect_to_database(self, collection_name: str = "product_embeddings"):
        """Connect to the ChromaDB database and collection."""
        logger.info(f"Connecting to ChromaDB at {self.db_path}")
        self.chroma_client = chromadb.PersistentClient(path=self.db_path)
        
        try:
            self.collection = self.chroma_client.get_collection(name=collection_name)
            logger.info(f"Connected to collection: {collection_name}")
            logger.info(f"Collection contains {self.collection.count()} documents")
        except Exception as e:
            logger.error(f"Failed to connect to collection {collection_name}: {e}")
            raise
    
    def semantic_search(self, query: str, n_results: int = 10, include_distances: bool = True) -> Dict[str, Any]:
        """
        Perform semantic search on the product database.
        
        Args:
            query: Search query string
            n_results: Number of results to return
            include_distances: Whether to include similarity distances
            
        Returns:
            Dictionary containing search results
        """
        if not self.embedding_model:
            raise ValueError("Embedding model not loaded. Call load_embedding_model() first.")
        
        if not self.collection:
            raise ValueError("Database not connected. Call connect_to_database() first.")
        
        logger.info(f"Searching for: '{query}' (top {n_results} results)")
        
        # Generate embedding for the query
        query_embedding = self.embedding_model.encode([query])
        
        # Perform search
        results = self.collection.query(
            query_embeddings=query_embedding.tolist(),
            n_results=n_results,
            include=['documents', 'metadatas', 'distances'] if include_distances else ['documents', 'metadatas']
        )
        
        return results
    
    def search_by_category(self, category: str, n_results: int = 10) -> Dict[str, Any]:
        """
        Search for products in a specific category.
        
        Args:
            category: Category name to search for
            n_results: Number of results to return
            
        Returns:
            Dictionary containing search results
        """
        if not self.collection:
            raise ValueError("Database not connected. Call connect_to_database() first.")
        
        logger.info(f"Searching by category: '{category}'")
        
        # Use ChromaDB's where clause to filter by category
        results = self.collection.query(
            query_texts=[category],
            n_results=n_results,
            where={"$or": [
                {"maincat": {"$contains": category}},
                {"subcat": {"$contains": category}}
            ]},
            include=['documents', 'metadatas', 'distances']
        )
        
        return results
    
    def search_by_brand(self, brand: str, n_results: int = 10) -> Dict[str, Any]:
        """
        Search for products by brand.
        
        Args:
            brand: Brand name to search for
            n_results: Number of results to return
            
        Returns:
            Dictionary containing search results
        """
        if not self.collection:
            raise ValueError("Database not connected. Call connect_to_database() first.")
        
        logger.info(f"Searching by brand: '{brand}'")
        
        results = self.collection.query(
            query_texts=[brand],
            n_results=n_results,
            where={"brand": {"$contains": brand}},
            include=['documents', 'metadatas', 'distances']
        )
        
        return results
    
    def get_product_by_mpn(self, mpn: str) -> Dict[str, Any]:
        """
        Get product(s) by MPN (Manufacturer Part Number).
        
        Args:
            mpn: MPN to search for
            
        Returns:
            Dictionary containing search results
        """
        if not self.collection:
            raise ValueError("Database not connected. Call connect_to_database() first.")
        
        logger.info(f"Searching by MPN: '{mpn}'")
        
        results = self.collection.query(
            query_texts=[mpn],
            n_results=10,  # May have multiple variants
            where={"mpn": mpn},
            include=['documents', 'metadatas', 'distances']
        )
        
        return results
    
    def format_search_results(self, results: Dict[str, Any], show_distances: bool = True) -> str:
        """
        Format search results for display.
        
        Args:
            results: Results from ChromaDB query
            show_distances: Whether to show similarity distances
            
        Returns:
            Formatted string representation of results
        """
        if not results['documents'] or not results['documents'][0]:
            return "No results found."
        
        formatted = []
        documents = results['documents'][0]
        metadatas = results['metadatas'][0]
        distances = results.get('distances', [None])[0] if show_distances else [None] * len(documents)
        
        for i, (doc, metadata, distance) in enumerate(zip(documents, metadatas, distances)):
            result_str = f"\n--- Result {i+1} ---"
            result_str += f"\nDocument: {doc}"
            result_str += f"\nMPN: {metadata.get('mpn', 'N/A')}"
            result_str += f"\nName: {metadata.get('name', 'N/A')}"
            result_str += f"\nBrand: {metadata.get('brand', 'N/A')}"
            result_str += f"\nCategory: {metadata.get('maincat', 'N/A')}"
            result_str += f"\nSubcategory: {metadata.get('subcat', 'N/A')}"
            result_str += f"\nSeller: {metadata.get('seller', 'N/A')}"
            
            if distance is not None:
                result_str += f"\nSimilarity Distance: {distance:.4f}"
            
            if metadata.get('url'):
                result_str += f"\nURL: {metadata['url']}"
            
            formatted.append(result_str)
        
        return "\n".join(formatted)


def run_sample_queries():
    """Run sample queries to test the search functionality."""
    
    # Initialize searcher
    searcher = ProductSearcher()
    
    try:
        # Load model and connect to database
        searcher.load_embedding_model()
        searcher.connect_to_database()
        
        # Sample queries for testing
        test_queries = [
            "dental acrylic resin",
            "orthodontic retainer material",
            "denture repair kit",
            "Keystone Dental products",
            "self-cure acrylic",
            "heat cure powder",
            "white dental material",
            "liquid acrylic"
        ]
        
        print("=" * 80)
        print("PRODUCT SEARCH TESTING")
        print("=" * 80)
        
        for query in test_queries:
            print(f"\n🔍 QUERY: '{query}'")
            print("-" * 60)
            
            results = searcher.semantic_search(query, n_results=3)
            formatted_results = searcher.format_search_results(results)
            print(formatted_results)
            
            print("\n" + "="*60)
        
        # Test specific searches
        print("\n\n🏷️  BRAND SEARCH TEST: 'Keystone Dental'")
        print("-" * 60)
        brand_results = searcher.search_by_brand("Keystone Dental", n_results=3)
        print(searcher.format_search_results(brand_results))
        
        print("\n\n📂 CATEGORY SEARCH TEST: 'Acrylics'")
        print("-" * 60)
        category_results = searcher.search_by_category("Acrylics", n_results=3)
        print(searcher.format_search_results(category_results))
        
        # Test MPN search
        print("\n\n🔢 MPN SEARCH TEST: '6010049'")
        print("-" * 60)
        mpn_results = searcher.get_product_by_mpn("6010049")
        print(searcher.format_search_results(mpn_results))
        
    except Exception as e:
        logger.error(f"Error during testing: {e}")
        raise


if __name__ == "__main__":
    run_sample_queries()
