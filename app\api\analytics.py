"""
Analytics API Endpoints
=======================

FastAPI endpoints for analytics and reporting.
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc
import logging
from datetime import datetime, timedelta

from app.database import get_db
from app.models.search_history import SearchHistory
from app.models.product_analytics import ProductAnalytics

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/search-trends")
async def get_search_trends(
    days: int = 7,
    db: AsyncSession = Depends(get_db)
):
    """Get search trends for the specified number of days."""
    try:
        since_date = datetime.utcnow() - timedelta(days=days)
        
        # Get search trends
        result = await db.execute(
            select(
                func.date(SearchHistory.searched_at).label('date'),
                func.count(SearchHistory.id).label('search_count'),
                func.avg(SearchHistory.search_duration_ms).label('avg_duration')
            )
            .where(SearchHistory.searched_at >= since_date)
            .group_by(func.date(SearchHistory.searched_at))
            .order_by(func.date(SearchHistory.searched_at))
        )
        
        trends = [
            {
                "date": row.date.isoformat(),
                "search_count": row.search_count,
                "avg_duration_ms": round(row.avg_duration, 2) if row.avg_duration else 0
            }
            for row in result
        ]
        
        return {"trends": trends, "period_days": days}
        
    except Exception as e:
        logger.error(f"Error getting search trends: {e}")
        raise HTTPException(status_code=500, detail="Failed to get search trends")


@router.get("/popular-products")
async def get_popular_products(
    limit: int = 10,
    db: AsyncSession = Depends(get_db)
):
    """Get most popular products based on analytics."""
    try:
        result = await db.execute(
            select(ProductAnalytics)
            .order_by(desc(ProductAnalytics.popularity_score))
            .limit(limit)
        )
        
        products = [product.to_dict() for product in result.scalars()]
        
        return {"popular_products": products, "limit": limit}
        
    except Exception as e:
        logger.error(f"Error getting popular products: {e}")
        raise HTTPException(status_code=500, detail="Failed to get popular products")
