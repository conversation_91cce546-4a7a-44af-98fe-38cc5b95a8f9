"""
Search API Endpoints
===================

FastAPI endpoints for product search functionality.
"""

from fastapi import APIRouter, Depends, HTTPException, Request, Query
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
import logging
import time
from typing import Optional

from app.database import get_db
from app.services.search_service import SearchService
from app.schemas.search import (
    SearchRequest, SearchResponse, SearchError,
    MPNSearchRequest, MPNSearchResponse,
    AutocompleteRequest, AutocompleteResponse,
    ProductMatch, BrandGroup, SearchPerformance, BrandDistribution
)
from app.models.search_history import SearchHistory
from app.models.user_session import UserSession

logger = logging.getLogger(__name__)
router = APIRouter()


def get_search_service(request: Request) -> SearchService:
    """Get search service from app state."""
    return request.app.state.search_service


@router.post("/", response_model=SearchResponse)
async def search_products(
    search_request: SearchRequest,
    request: Request,
    db: AsyncSession = Depends(get_db),
    search_service: SearchService = Depends(get_search_service)
):
    """
    Perform semantic product search.
    
    This endpoint provides comprehensive product search with semantic similarity
    matching, categorized results, and performance metrics.
    """
    start_time = time.time()
    
    try:
        # Perform search
        results = await search_service.semantic_search(
            query=search_request.query,
            max_results=search_request.max_results,
            similarity_threshold=search_request.similarity_threshold
        )
        
        # Convert results to response format
        response_data = _convert_search_results_to_response(results)
        
        # Log search for analytics (async, don't wait)
        try:
            await _log_search_history(
                db=db,
                request=request,
                search_request=search_request,
                results=results,
                search_duration_ms=response_data['performance'].search_duration_ms
            )
        except Exception as e:
            logger.warning(f"Failed to log search history: {e}")
        
        return SearchResponse(**response_data)
        
    except Exception as e:
        logger.error(f"Search error for query '{search_request.query}': {e}")
        raise HTTPException(
            status_code=500,
            detail=SearchError(
                error=str(e),
                error_type="search_error",
                query=search_request.query
            ).dict()
        )


@router.get("/mpn/{mpn}", response_model=MPNSearchResponse)
async def search_by_mpn(
    mpn: str,
    request: Request,
    db: AsyncSession = Depends(get_db),
    search_service: SearchService = Depends(get_search_service)
):
    """
    Search for products by Manufacturer Part Number (MPN).
    
    This endpoint finds all product variants with the specified MPN
    and groups them by brand with similarity scoring.
    """
    try:
        # Perform MPN search
        results = await search_service.mpn_search(mpn)
        
        # Convert results to response format
        response_data = _convert_mpn_results_to_response(results)
        
        # Log search for analytics
        try:
            search_request = MPNSearchRequest(mpn=mpn)
            await _log_search_history(
                db=db,
                request=request,
                search_request=search_request,
                results=results,
                search_duration_ms=response_data['performance'].search_duration_ms,
                search_type="mpn"
            )
        except Exception as e:
            logger.warning(f"Failed to log MPN search history: {e}")
        
        return MPNSearchResponse(**response_data)
        
    except Exception as e:
        logger.error(f"MPN search error for '{mpn}': {e}")
        raise HTTPException(
            status_code=500,
            detail=SearchError(
                error=str(e),
                error_type="mpn_search_error",
                query=mpn
            ).dict()
        )


@router.get("/autocomplete", response_model=AutocompleteResponse)
async def autocomplete_search(
    q: str = Query(..., min_length=2, max_length=200, description="Search query"),
    max_suggestions: Optional[int] = Query(default=10, ge=1, le=20, description="Maximum suggestions"),
    search_service: SearchService = Depends(get_search_service)
):
    """
    Get autocomplete suggestions for search queries.
    
    This endpoint provides real-time autocomplete suggestions based on
    semantic similarity to help users find products quickly.
    """
    try:
        # Get autocomplete suggestions
        suggestions = await search_service.autocomplete_search(
            query=q,
            max_results=max_suggestions
        )
        
        return AutocompleteResponse(
            query=q,
            suggestions=suggestions,
            total_suggestions=len(suggestions)
        )
        
    except Exception as e:
        logger.error(f"Autocomplete error for query '{q}': {e}")
        # For autocomplete, return empty results instead of error
        return AutocompleteResponse(
            query=q,
            suggestions=[],
            total_suggestions=0
        )


@router.get("/suggestions/{query}")
async def get_search_suggestions(
    query: str,
    limit: Optional[int] = Query(default=5, ge=1, le=10),
    search_service: SearchService = Depends(get_search_service)
):
    """
    Get quick search suggestions (lightweight endpoint for real-time typing).
    
    This is a simplified version of autocomplete optimized for real-time
    suggestions as users type.
    """
    if len(query.strip()) < 2:
        return {"suggestions": []}
    
    try:
        suggestions = await search_service.autocomplete_search(
            query=query,
            max_results=limit
        )
        
        # Return simplified format for frontend
        return {
            "suggestions": [
                {
                    "text": s.text,
                    "brand": s.brand,
                    "category": s.category
                }
                for s in suggestions
            ]
        }
        
    except Exception as e:
        logger.error(f"Suggestions error for query '{query}': {e}")
        return {"suggestions": []}


def _convert_search_results_to_response(results: dict) -> dict:
    """Convert search service results to API response format."""
    
    def convert_match(match: dict) -> dict:
        return {
            "product_id": match.get('metadata', {}).get('variant_index', match.get('mpn', '')),
            "mpn": match['mpn'],
            "name": match['name'],
            "brand": match['brand'],
            "category": match['category'],
            "subcategory": match.get('subcategory', ''),
            "seller": match.get('seller', ''),
            "price": None,  # Price not available in current data
            "price_currency": "USD",
            "product_url": match.get('url', ''),
            "similarity_score": match['similarity_score'],
            "similarity_distance": match['similarity_distance'],
            "metadata": match.get('metadata', {})
        }
    
    return {
        "query": results['query'],
        "exact_matches": [convert_match(m) for m in results.get('exact_matches', [])],
        "high_confidence_similar": [convert_match(m) for m in results.get('high_confidence_similar', [])],
        "medium_confidence_similar": [convert_match(m) for m in results.get('medium_confidence_similar', [])],
        "category_related": [convert_match(m) for m in results.get('category_related', [])],
        "total_results": results.get('total_results', 0),
        "brand_distribution": results.get('brand_distribution'),
        "performance": results.get('performance', {})
    }


def _convert_mpn_results_to_response(results: dict) -> dict:
    """Convert MPN search results to API response format."""
    
    brand_groups = []
    for brand, products in results.get('brand_groups', {}).items():
        brand_group = {
            "brand": brand,
            "similarity_score": results.get('brand_similarity_scores', {}).get(brand, 0.0),
            "products": []
        }
        
        for product in products:
            brand_group["products"].append({
                "product_id": product.get('variant_index', product.get('mpn', '')),
                "mpn": product['metadata']['mpn'],
                "name": product['name'],
                "brand": product['brand'],
                "category": product['category'],
                "subcategory": product.get('subcategory', ''),
                "seller": product.get('seller', ''),
                "price": None,
                "price_currency": "USD",
                "product_url": product.get('url', ''),
                "similarity_score": 1.0 - product['similarity_distance'],  # Convert distance to score
                "similarity_distance": product['similarity_distance'],
                "metadata": product.get('metadata', {})
            })
        
        brand_groups.append(brand_group)
    
    return {
        "mpn": results['mpn'],
        "total_variants": results.get('total_variants', 0),
        "brand_groups": brand_groups,
        "brand_similarity_scores": results.get('brand_similarity_scores', {}),
        "performance": results.get('performance', {})
    }


async def _log_search_history(
    db: AsyncSession,
    request: Request,
    search_request,
    results: dict,
    search_duration_ms: float,
    search_type: str = "semantic"
):
    """Log search history for analytics."""
    try:
        # Get or create session (simplified for now)
        session_token = request.headers.get("X-Session-Token", "anonymous")
        
        # Create search history record
        search_history = SearchHistory(
            session_id=None,  # Will be linked when session management is implemented
            search_query=getattr(search_request, 'query', getattr(search_request, 'mpn', '')),
            search_type=search_type,
            max_results_requested=getattr(search_request, 'max_results', 20),
            similarity_threshold=getattr(search_request, 'similarity_threshold', 0.3),
            total_results_found=results.get('total_results', 0),
            exact_matches_count=len(results.get('exact_matches', [])),
            high_confidence_matches_count=len(results.get('high_confidence_matches', [])),
            similar_products_count=len(results.get('medium_confidence_similar', [])),
            category_related_count=len(results.get('category_related', [])),
            search_duration_ms=int(search_duration_ms),
            brand_distribution=results.get('brand_distribution', {}),
            top_results=results.get('exact_matches', [])[:5]  # Store top 5 results
        )
        
        db.add(search_history)
        await db.commit()
        
    except Exception as e:
        logger.error(f"Failed to log search history: {e}")
        await db.rollback()
