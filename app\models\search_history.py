"""
Search History Model
===================

Model for tracking user search queries and results for analytics.
"""

from sqlalchemy import Column, String, DateTime, Integer, Float, Text, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
import uuid

from app.database import Base


class SearchHistory(Base):
    """
    Search history model for tracking user search behavior.
    
    This model stores all search queries made by users, along with
    metadata about the search results and user interactions. This data
    is valuable for analytics and improving search functionality.
    """
    
    __tablename__ = "search_history"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Foreign key to user session
    session_id = Column(UUID(as_uuid=True), ForeignKey("user_sessions.id", ondelete="CASCADE"), nullable=False)
    
    # Search details
    search_query = Column(Text, nullable=False, index=True)
    search_type = Column(String(50), nullable=False, default="semantic")  # semantic, mpn, autocomplete
    
    # Search parameters
    max_results_requested = Column(Integer, default=20)
    similarity_threshold = Column(Float, default=0.3)
    
    # Search results metadata
    total_results_found = Column(Integer, default=0)
    exact_matches_count = Column(Integer, default=0)
    high_confidence_matches_count = Column(Integer, default=0)
    similar_products_count = Column(Integer, default=0)
    category_related_count = Column(Integer, default=0)
    
    # Performance metrics
    search_duration_ms = Column(Integer)  # Search execution time in milliseconds
    embedding_generation_ms = Column(Integer)  # Time to generate query embedding
    
    # User interaction
    results_clicked = Column(Integer, default=0)  # Number of results user clicked
    items_added_to_wishlist = Column(Integer, default=0)  # Items added from this search
    
    # Timestamps
    searched_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Search results (top results for analysis)
    top_results = Column(JSONB, default=list)  # Store top 5 results with metadata
    
    # Brand distribution in results
    brand_distribution = Column(JSONB, default=dict)  # Brand counts in results
    
    # Error information (if search failed)
    error_message = Column(Text)
    error_type = Column(String(100))
    
    # Additional metadata
    metadata = Column(JSONB, default=dict)
    
    # Indexes for analytics queries
    __table_args__ = (
        Index('idx_search_session_time', 'session_id', 'searched_at'),
        Index('idx_search_query_type', 'search_query', 'search_type'),
        Index('idx_search_performance', 'search_duration_ms'),
        Index('idx_search_results_count', 'total_results_found'),
    )
    
    def __repr__(self):
        return f"<SearchHistory(id={self.id}, query='{self.search_query[:50]}...', results={self.total_results_found})>"
    
    def to_dict(self):
        """Convert search history to dictionary."""
        return {
            "id": str(self.id),
            "session_id": str(self.session_id),
            "search_query": self.search_query,
            "search_type": self.search_type,
            "max_results_requested": self.max_results_requested,
            "similarity_threshold": self.similarity_threshold,
            "total_results_found": self.total_results_found,
            "exact_matches_count": self.exact_matches_count,
            "high_confidence_matches_count": self.high_confidence_matches_count,
            "similar_products_count": self.similar_products_count,
            "category_related_count": self.category_related_count,
            "search_duration_ms": self.search_duration_ms,
            "embedding_generation_ms": self.embedding_generation_ms,
            "results_clicked": self.results_clicked,
            "items_added_to_wishlist": self.items_added_to_wishlist,
            "searched_at": self.searched_at.isoformat() if self.searched_at else None,
            "top_results": self.top_results,
            "brand_distribution": self.brand_distribution,
            "error_message": self.error_message,
            "error_type": self.error_type,
            "metadata": self.metadata,
        }
