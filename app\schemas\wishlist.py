"""
Wishlist API Schemas
===================

Pydantic schemas for wishlist API requests and responses.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
import uuid


class AddToWishlistRequest(BaseModel):
    """Schema for adding products to wishlist."""
    
    product_id: str = Field(..., description="Product ID from search results")
    mpn: str = Field(..., description="Manufacturer Part Number")
    product_name: str = Field(..., min_length=1, max_length=500, description="Product name")
    brand: str = Field(..., min_length=1, max_length=255, description="Brand name")
    category: Optional[str] = Field(None, max_length=255, description="Main category")
    subcategory: Optional[str] = Field(None, max_length=255, description="Subcategory")
    seller: Optional[str] = Field(None, max_length=255, description="Seller name")
    price: Optional[float] = Field(None, ge=0, description="Product price")
    price_currency: Optional[str] = Field(default="USD", max_length=10, description="Price currency")
    product_url: Optional[str] = Field(None, description="Product URL")
    search_query: Optional[str] = Field(None, description="Search query that found this product")
    similarity_score: Optional[float] = Field(None, ge=0, le=1, description="Similarity score")
    similarity_distance: Optional[float] = Field(None, ge=0, description="Similarity distance")
    user_notes: Optional[str] = Field(None, max_length=1000, description="User notes")
    priority: Optional[int] = Field(default=0, description="Priority within wishlist")
    
    @validator('product_name', 'brand')
    def validate_required_strings(cls, v):
        """Validate required string fields."""
        if not v.strip():
            raise ValueError('Field cannot be empty')
        return v.strip()


class UpdateWishlistItemRequest(BaseModel):
    """Schema for updating wishlist items."""
    
    user_notes: Optional[str] = Field(None, max_length=1000, description="User notes")
    priority: Optional[int] = Field(None, description="Priority within wishlist")
    price: Optional[float] = Field(None, ge=0, description="Updated price")


class WishlistItemResponse(BaseModel):
    """Schema for wishlist item responses."""
    
    id: str = Field(..., description="Wishlist item ID")
    session_id: str = Field(..., description="User session ID")
    product_id: str = Field(..., description="Product ID")
    mpn: str = Field(..., description="Manufacturer Part Number")
    product_name: str = Field(..., description="Product name")
    brand: str = Field(..., description="Brand name")
    category: Optional[str] = Field(None, description="Main category")
    subcategory: Optional[str] = Field(None, description="Subcategory")
    seller: Optional[str] = Field(None, description="Seller name")
    price: Optional[float] = Field(None, description="Product price")
    price_currency: Optional[str] = Field(None, description="Price currency")
    product_url: Optional[str] = Field(None, description="Product URL")
    search_query: Optional[str] = Field(None, description="Search query that found this product")
    similarity_score: Optional[float] = Field(None, description="Similarity score")
    similarity_distance: Optional[float] = Field(None, description="Similarity distance")
    added_at: datetime = Field(..., description="When item was added to wishlist")
    updated_at: datetime = Field(..., description="When item was last updated")
    user_notes: Optional[str] = Field(None, description="User notes")
    priority: int = Field(..., description="Priority within wishlist")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class WishlistResponse(BaseModel):
    """Schema for complete wishlist responses."""
    
    session_id: str = Field(..., description="User session ID")
    items: List[WishlistItemResponse] = Field(..., description="Wishlist items")
    total_items: int = Field(..., description="Total number of items in wishlist")
    total_brands: int = Field(..., description="Number of unique brands")
    total_categories: int = Field(..., description="Number of unique categories")
    average_price: Optional[float] = Field(None, description="Average price of items with prices")
    price_range: Optional[Dict[str, float]] = Field(None, description="Price range (min/max)")
    last_updated: datetime = Field(..., description="When wishlist was last updated")


class WishlistSummary(BaseModel):
    """Schema for wishlist summary."""
    
    total_items: int = Field(..., description="Total number of items")
    brands: List[str] = Field(..., description="List of brands in wishlist")
    categories: List[str] = Field(..., description="List of categories in wishlist")
    price_stats: Optional[Dict[str, Any]] = Field(None, description="Price statistics")
    recent_additions: List[WishlistItemResponse] = Field(..., description="Recently added items")


class BulkWishlistOperation(BaseModel):
    """Schema for bulk wishlist operations."""
    
    operation: str = Field(..., description="Operation type: add, remove, update, clear")
    items: Optional[List[AddToWishlistRequest]] = Field(None, description="Items for bulk add")
    item_ids: Optional[List[str]] = Field(None, description="Item IDs for bulk remove")
    updates: Optional[Dict[str, UpdateWishlistItemRequest]] = Field(None, description="Updates by item ID")


class WishlistOperationResponse(BaseModel):
    """Schema for wishlist operation responses."""
    
    success: bool = Field(..., description="Whether operation was successful")
    message: str = Field(..., description="Operation result message")
    affected_items: int = Field(..., description="Number of items affected")
    item_id: Optional[str] = Field(None, description="ID of affected item (for single operations)")
    errors: Optional[List[str]] = Field(None, description="Any errors that occurred")


class WishlistError(BaseModel):
    """Schema for wishlist error responses."""
    
    error: str = Field(..., description="Error message")
    error_type: str = Field(..., description="Error type")
    item_id: Optional[str] = Field(None, description="Item ID that caused the error")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")
