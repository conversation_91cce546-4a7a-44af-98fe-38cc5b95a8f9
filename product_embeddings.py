#!/usr/bin/env python3
"""
Product Embeddings with ChromaDB
================================

This script processes product data from all.json and creates embeddings using ChromaDB.
It handles the nested structure where each MPN can have multiple product variants from different sellers.
"""

import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
import chromadb
from sentence_transformers import SentenceTransformer
import uuid

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProductEmbeddingProcessor:
    """Processes product data and creates embeddings for ChromaDB storage."""
    
    def __init__(self, model_name: str = "BAAI/bge-large-en-v1.5", db_path: str = "./chroma_db"):
        """
        Initialize the processor with embedding model and database path.

        Args:
            model_name: Name of the sentence transformer model to use
                      Default: BAAI/bge-large-en-v1.5 - One of the best performing models
                      for semantic similarity and retrieval tasks as of 2024
            db_path: Path to store ChromaDB data
        """
        self.model_name = model_name
        self.db_path = db_path
        self.embedding_model = None
        self.chroma_client = None
        self.collection = None
        
    def load_embedding_model(self):
        """
        Load the sentence transformer model for embeddings.

        Model Choice Justification:
        - BAAI/bge-large-en-v1.5: Top performer on MTEB leaderboard for retrieval tasks
        - Optimized for semantic similarity and search applications
        - Better than older models like all-MiniLM-L6-v2 for e-commerce use cases
        - Alternative options: Alibaba-NLP/gte-large-en-v1.5, intfloat/e5-large-v2
        """
        logger.info(f"Loading embedding model: {self.model_name}")
        self.embedding_model = SentenceTransformer(self.model_name)
        logger.info("Embedding model loaded successfully")
        
    def initialize_chromadb(self, collection_name: str = "product_embeddings", reset_collection: bool = False):
        """
        Initialize ChromaDB client and collection.

        Args:
            collection_name: Name of the collection to create/use
            reset_collection: If True, delete existing collection and create new one
        """
        logger.info(f"Initializing ChromaDB at {self.db_path}")
        self.chroma_client = chromadb.PersistentClient(path=self.db_path)

        # Handle existing collection
        if reset_collection:
            try:
                self.chroma_client.delete_collection(name=collection_name)
                logger.info(f"Deleted existing collection: {collection_name}")
            except Exception:
                pass  # Collection might not exist

        # Create or get collection
        try:
            self.collection = self.chroma_client.create_collection(
                name=collection_name,
                metadata={
                    "description": "Product embeddings for e-commerce search",
                    "embedding_model": self.model_name,
                    "created_at": str(uuid.uuid4()),  # Simple timestamp alternative
                    "data_source": "all.json",
                    "document_format": "name | brand | maincat | subcat"
                }
            )
            logger.info(f"Created new collection: {collection_name}")
        except Exception as e:
            # Collection might already exist
            try:
                self.collection = self.chroma_client.get_collection(name=collection_name)
                logger.info(f"Using existing collection: {collection_name}")
                logger.info(f"Collection metadata: {self.collection.metadata}")
            except Exception as get_error:
                logger.error(f"Failed to create or get collection: {get_error}")
                raise
    
    def load_product_data(self, json_file_path: str) -> List[Dict[str, Any]]:
        """
        Load product data from JSON file.
        
        Args:
            json_file_path: Path to the all.json file
            
        Returns:
            List of product data dictionaries
        """
        logger.info(f"Loading product data from {json_file_path}")
        
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info(f"Loaded {len(data)} MPN entries from JSON file")
            return data
            
        except FileNotFoundError:
            logger.error(f"File not found: {json_file_path}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON format: {e}")
            raise
        except Exception as e:
            logger.error(f"Error loading JSON file: {e}")
            raise
    
    def create_document_text(self, product: Dict[str, Any], include_description: bool = False) -> str:
        """
        Create a semantically rich document string from product data.

        The document is structured to optimize semantic search by concatenating
        the most important fields in order of relevance for product matching.

        Args:
            product: Product dictionary containing name, brand, maincat, subcat
            include_description: Whether to include product description (can make docs very long)

        Returns:
            Concatenated document string for embedding
        """
        # Extract fields with fallbacks for missing data and handle None values
        name = product.get('name', '') or ''
        name = str(name).strip() if name is not None else ''

        brand = product.get('brand', '') or ''
        brand = str(brand).strip() if brand is not None else ''

        maincat = product.get('maincat', '') or ''
        maincat = str(maincat).strip() if maincat is not None else ''

        subcat = product.get('subcat', '') or ''
        subcat = str(subcat).strip() if subcat is not None else ''

        description = ''
        if include_description:
            desc = product.get('description', '') or ''
            description = str(desc).strip() if desc is not None else ''

        # Build document parts in order of importance for search
        parts = []

        # 1. Product name (most important)
        if name:
            parts.append(name)

        # 2. Brand (very important for product identification)
        if brand:
            parts.append(brand)

        # 3. Main category (important for categorization)
        if maincat:
            # Clean up maincat (remove trailing slashes and extra spaces)
            maincat_clean = maincat.rstrip(' /').strip()
            if maincat_clean:
                parts.append(maincat_clean)

        # 4. Subcategory (provides more specific context)
        if subcat:
            parts.append(subcat)

        # 5. Description (optional, can be very long)
        if description and include_description:
            # Truncate description if too long to avoid overwhelming the embedding
            if len(description) > 200:
                description = description[:200] + "..."
            parts.append(description)

        # Join with separator for readability and parsing
        document_text = " | ".join(parts)

        # Fallback for completely empty products
        if not document_text:
            return "Unknown Product"

        return document_text
    
    def process_products_for_embedding(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process the nested product data structure for embedding.

        Args:
            data: Raw data from JSON file

        Returns:
            List of processed product records ready for embedding
        """
        processed_products = []
        skipped_entries = 0
        skipped_products = 0

        logger.info(f"Processing {len(data)} MPN entries...")

        for entry_idx, mpn_entry in enumerate(data):
            if entry_idx % 100 == 0:
                logger.info(f"Processing entry {entry_idx + 1}/{len(data)}")

            mpn = mpn_entry.get('mpn', '')
            if mpn is None:
                mpn = ''
            else:
                mpn = str(mpn).strip()

            products = mpn_entry.get('products', [])

            if not mpn:
                logger.warning(f"Skipping entry {entry_idx} with missing MPN")
                skipped_entries += 1
                continue

            if not products:
                logger.warning(f"No products found for MPN: {mpn}")
                skipped_entries += 1
                continue

            # Process each product variant for this MPN
            for idx, product in enumerate(products):
                try:
                    # Validate product has minimum required data
                    if not product.get('name', '').strip():
                        logger.warning(f"Skipping product {idx} for MPN {mpn}: missing name")
                        skipped_products += 1
                        continue

                    # Create document text for embedding
                    document_text = self.create_document_text(product)

                    # Skip if document is too short to be meaningful
                    if len(document_text.strip()) < 3:
                        logger.warning(f"Skipping product {idx} for MPN {mpn}: document too short")
                        skipped_products += 1
                        continue

                    # Create unique ID for this product variant
                    # Using MPN + index to handle multiple products per MPN
                    product_id = f"{mpn}_{idx}" if len(products) > 1 else mpn

                    # Prepare metadata (store original product data, handle None values)
                    metadata = {
                        'mpn': mpn,
                        'name': str(product.get('name', '') or ''),
                        'brand': str(product.get('brand', '') or ''),
                        'maincat': str(product.get('maincat', '') or ''),
                        'subcat': str(product.get('subcat', '') or ''),
                        'description': str(product.get('description', '') or ''),
                        'price': str(product.get('price', '') or '') if product.get('price') is not None else '',
                        'url': str(product.get('url', '') or ''),
                        'seller': str(product.get('seller', '') or ''),
                        'variant_index': idx,
                        'total_variants': len(products)
                    }

                    processed_products.append({
                        'id': product_id,
                        'document': document_text,
                        'metadata': metadata
                    })

                except Exception as e:
                    logger.error(f"Error processing product {idx} for MPN {mpn}: {e}")
                    skipped_products += 1
                    continue

        logger.info(f"Processing complete:")
        logger.info(f"  - Processed: {len(processed_products)} product variants")
        logger.info(f"  - Skipped entries: {skipped_entries}")
        logger.info(f"  - Skipped products: {skipped_products}")

        return processed_products
    
    def generate_embeddings_and_store(self, processed_products: List[Dict[str, Any]], batch_size: int = 100):
        """
        Generate embeddings and store in ChromaDB.
        
        Args:
            processed_products: List of processed product records
            batch_size: Number of products to process in each batch
        """
        if not self.embedding_model:
            raise ValueError("Embedding model not loaded. Call load_embedding_model() first.")
        
        if not self.collection:
            raise ValueError("ChromaDB collection not initialized. Call initialize_chromadb() first.")
        
        logger.info(f"Generating embeddings and storing {len(processed_products)} products in batches of {batch_size}")
        
        # Process in batches to manage memory
        for i in range(0, len(processed_products), batch_size):
            batch = processed_products[i:i + batch_size]
            
            try:
                # Extract data for this batch
                ids = [product['id'] for product in batch]
                documents = [product['document'] for product in batch]
                metadatas = [product['metadata'] for product in batch]
                
                # Generate embeddings for the batch
                logger.info(f"Processing batch {i//batch_size + 1}/{(len(processed_products) + batch_size - 1)//batch_size}")
                embeddings = self.embedding_model.encode(documents, show_progress_bar=True)
                
                # Store in ChromaDB
                self.collection.add(
                    ids=ids,
                    embeddings=embeddings.tolist(),
                    documents=documents,
                    metadatas=metadatas
                )
                
                logger.info(f"Stored batch {i//batch_size + 1} successfully")
                
            except Exception as e:
                logger.error(f"Error processing batch starting at index {i}: {e}")
                continue
        
        logger.info("Embedding generation and storage completed")
    
    def get_collection_info(self) -> Dict[str, Any]:
        """Get information about the current collection."""
        if not self.collection:
            return {"error": "Collection not initialized"}
        
        count = self.collection.count()
        return {
            "collection_name": self.collection.name,
            "document_count": count,
            "metadata": self.collection.metadata
        }


def main():
    """Main function to process product data and create embeddings."""
    # Configuration
    JSON_FILE = "all.json"
    MODEL_NAME = "BAAI/bge-large-en-v1.5"  # Best performing model for e-commerce/semantic search
    DB_PATH = "./chroma_db"
    COLLECTION_NAME = "product_embeddings"
    
    try:
        # Initialize processor
        processor = ProductEmbeddingProcessor(model_name=MODEL_NAME, db_path=DB_PATH)
        
        # Load embedding model
        processor.load_embedding_model()
        
        # Initialize ChromaDB
        processor.initialize_chromadb(collection_name=COLLECTION_NAME)
        
        # Load and process data
        raw_data = processor.load_product_data(JSON_FILE)
        processed_products = processor.process_products_for_embedding(raw_data)
        
        # Generate embeddings and store
        processor.generate_embeddings_and_store(processed_products)
        
        # Print collection info
        info = processor.get_collection_info()
        logger.info(f"Collection info: {info}")
        
        logger.info("Product embedding process completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in main process: {e}")
        raise


if __name__ == "__main__":
    main()
