# Application Configuration
DEBUG=false
HOST=0.0.0.0
PORT=8000

# PostgreSQL Database
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_password_here
POSTGRES_DB=product_search

# ChromaDB
CHROMA_DB_PATH=./chroma_db
CHROMA_COLLECTION_NAME=product_embeddings

# Embedding Model
EMBEDDING_MODEL_NAME=BAAI/bge-large-en-v1.5

# Search Configuration
SEARCH_MAX_RESULTS=20
SIMILARITY_THRESHOLD=0.3
AUTOCOMPLETE_MAX_RESULTS=10

# Security
SECRET_KEY=your-secret-key-change-in-production
SESSION_EXPIRE_HOURS=24

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# CORS Origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://localhost:8000,http://127.0.0.1:8000

# Logging
LOG_LEVEL=INFO

# Performance
MAX_CONCURRENT_SEARCHES=10
SEARCH_TIMEOUT_SECONDS=30
