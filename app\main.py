"""
FastAPI Main Application
========================

Main FastAPI application with all routes, middleware, and startup/shutdown events.
"""

from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from contextlib import asynccontextmanager
import logging
import time
import uuid

from app.config import get_settings
from app.database import init_db, close_db
from app.services.search_service import SearchService
from app.api import search, wishlist, analytics, session

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting Product Search & Price Comparison Application")
    
    # Initialize database
    await init_db()
    logger.info("Database initialized")
    
    # Initialize search service
    search_service = SearchService()
    await search_service.initialize()
    app.state.search_service = search_service
    logger.info("Search service initialized")
    
    yield
    
    # Shutdown
    logger.info("Shutting down application")
    await close_db()
    logger.info("Database connections closed")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Advanced product search and price comparison platform with semantic similarity matching",
    lifespan=lifespan,
    docs_url="/api/docs",
    redoc_url="/api/redoc",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """Add processing time header to responses."""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


@app.middleware("http")
async def add_request_id_header(request: Request, call_next):
    """Add unique request ID to responses for tracking."""
    request_id = str(uuid.uuid4())
    request.state.request_id = request_id
    response = await call_next(request)
    response.headers["X-Request-ID"] = request_id
    return response


# Include API routers
app.include_router(search.router, prefix="/api/search", tags=["search"])
app.include_router(wishlist.router, prefix="/api/wishlist", tags=["wishlist"])
app.include_router(analytics.router, prefix="/api/analytics", tags=["analytics"])
app.include_router(session.router, prefix="/api/session", tags=["session"])

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")


@app.get("/", response_class=HTMLResponse)
async def read_root():
    """Serve the main application page."""
    return """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Product Search & Price Comparison</title>
        <link rel="stylesheet" href="/static/css/main.css">
    </head>
    <body>
        <div id="app">
            <header class="header">
                <div class="container">
                    <h1 class="logo">Product Search & Price Comparison</h1>
                    <nav class="nav">
                        <a href="#search" class="nav-link">Search</a>
                        <a href="#wishlist" class="nav-link">Wishlist</a>
                        <a href="#compare" class="nav-link">Compare</a>
                    </nav>
                </div>
            </header>
            
            <main class="main">
                <div class="container">
                    <!-- Search Section -->
                    <section id="search-section" class="section">
                        <div class="search-container">
                            <h2>Find Products</h2>
                            <div class="search-box">
                                <input 
                                    type="text" 
                                    id="search-input" 
                                    placeholder="Search for products, brands, or MPNs..."
                                    class="search-input"
                                >
                                <div id="search-suggestions" class="search-suggestions"></div>
                            </div>
                            <div id="search-results" class="search-results"></div>
                        </div>
                    </section>
                    
                    <!-- Wishlist Section -->
                    <section id="wishlist-section" class="section">
                        <div class="wishlist-container">
                            <h2>Your Wishlist</h2>
                            <div id="wishlist-items" class="wishlist-items"></div>
                            <div class="wishlist-actions">
                                <button id="compare-prices-btn" class="btn btn-primary">Compare Prices</button>
                                <button id="clear-wishlist-btn" class="btn btn-secondary">Clear Wishlist</button>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Price Comparison Section -->
                    <section id="comparison-section" class="section">
                        <div class="comparison-container">
                            <h2>Price Comparison</h2>
                            <div id="comparison-results" class="comparison-results"></div>
                        </div>
                    </section>
                </div>
            </main>
            
            <footer class="footer">
                <div class="container">
                    <p>&copy; 2024 Product Search & Price Comparison. Powered by ChromaDB and AI.</p>
                </div>
            </footer>
        </div>
        
        <!-- Loading overlay -->
        <div id="loading-overlay" class="loading-overlay">
            <div class="loading-spinner"></div>
            <p>Searching products...</p>
        </div>
        
        <script src="/static/js/main.js"></script>
    </body>
    </html>
    """


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "app_name": settings.app_name,
        "version": settings.app_version,
        "timestamp": time.time()
    }


@app.get("/api/info")
async def app_info():
    """Get application information."""
    return {
        "app_name": settings.app_name,
        "version": settings.app_version,
        "description": "Advanced product search and price comparison platform",
        "features": [
            "Semantic product search",
            "Real-time autocomplete",
            "Wishlist management",
            "Price comparison",
            "Cross-brand similarity detection",
            "MPN-based product grouping"
        ],
        "database_info": {
            "products_indexed": "219,796",
            "embedding_model": settings.embedding_model_name,
            "similarity_threshold": settings.similarity_threshold
        }
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
