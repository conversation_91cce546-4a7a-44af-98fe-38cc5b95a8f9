"""
Wishlist Model
=============

Model for managing user wishlist items and product selections.
"""

from sqlalchemy import Column, String, DateTime, Float, Integer, Text, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.database import Base


class WishlistItem(Base):
    """
    Wishlist item model for storing user-selected products.
    
    This model stores products that users have added to their wishlist
    for price comparison and tracking. Each item is linked to a user session
    and contains product metadata from ChromaDB.
    """
    
    __tablename__ = "wishlist_items"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Foreign key to user session
    session_id = Column(UUID(as_uuid=True), ForeignKey("user_sessions.id", ondelete="CASCADE"), nullable=False)
    
    # Product identification (from ChromaDB)
    product_id = Column(String(255), nullable=False)  # ChromaDB document ID
    mpn = Column(String(255), nullable=False, index=True)  # Manufacturer Part Number
    
    # Product details (cached from ChromaDB for performance)
    product_name = Column(Text, nullable=False)
    brand = Column(String(255), nullable=False, index=True)
    category = Column(String(255))
    subcategory = Column(String(255))
    seller = Column(String(255), index=True)
    
    # Pricing information
    price = Column(Float)  # Current price (if available)
    price_currency = Column(String(10), default="USD")
    
    # Product URL
    product_url = Column(Text)
    
    # Search context (how this product was found)
    search_query = Column(Text)
    similarity_score = Column(Float)  # Similarity score when found
    similarity_distance = Column(Float)  # Distance from search query
    
    # Timestamps
    added_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Additional metadata (flexible JSON storage)
    metadata = Column(JSONB, default=dict)
    
    # User notes
    user_notes = Column(Text)
    
    # Priority/ranking within wishlist
    priority = Column(Integer, default=0)
    
    # Relationship to user session (import will be handled by SQLAlchemy)
    # session = relationship("UserSession", backref="wishlist_items")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_wishlist_session_mpn', 'session_id', 'mpn'),
        Index('idx_wishlist_brand_category', 'brand', 'category'),
        Index('idx_wishlist_added_at', 'added_at'),
    )
    
    def __repr__(self):
        return f"<WishlistItem(id={self.id}, mpn={self.mpn}, product={self.product_name[:50]}...)>"
    
    def to_dict(self):
        """Convert wishlist item to dictionary."""
        return {
            "id": str(self.id),
            "session_id": str(self.session_id),
            "product_id": self.product_id,
            "mpn": self.mpn,
            "product_name": self.product_name,
            "brand": self.brand,
            "category": self.category,
            "subcategory": self.subcategory,
            "seller": self.seller,
            "price": self.price,
            "price_currency": self.price_currency,
            "product_url": self.product_url,
            "search_query": self.search_query,
            "similarity_score": self.similarity_score,
            "similarity_distance": self.similarity_distance,
            "added_at": self.added_at.isoformat() if self.added_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "metadata": self.metadata,
            "user_notes": self.user_notes,
            "priority": self.priority,
        }
