#!/usr/bin/env python3
"""
Application Runner
=================

Main entry point for running the Product Search & Price Comparison application.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent))

from app.main import app
from app.config import get_settings
import uvicorn

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("app.log")
    ]
)

logger = logging.getLogger(__name__)
settings = get_settings()


def check_prerequisites():
    """Check if all prerequisites are met before starting the application."""
    
    logger.info("Checking prerequisites...")
    
    # Check if ChromaDB exists
    chroma_path = Path(settings.chroma_db_path)
    if not chroma_path.exists():
        logger.error(f"ChromaDB not found at {chroma_path}")
        logger.error("Please run the embedding generation script first:")
        logger.error("python product_embeddings.py")
        return False
    
    # Check if collection exists
    try:
        import chromadb
        client = chromadb.PersistentClient(path=str(chroma_path))
        collection = client.get_collection(settings.chroma_collection_name)
        count = collection.count()
        logger.info(f"Found ChromaDB collection with {count:,} products")
        
        if count == 0:
            logger.warning("ChromaDB collection is empty")
            return False
            
    except Exception as e:
        logger.error(f"Failed to connect to ChromaDB: {e}")
        return False
    
    # Check if embedding model is available
    try:
        from sentence_transformers import SentenceTransformer
        model = SentenceTransformer(settings.embedding_model_name)
        logger.info(f"Embedding model {settings.embedding_model_name} loaded successfully")
    except Exception as e:
        logger.error(f"Failed to load embedding model: {e}")
        return False
    
    logger.info("✅ All prerequisites met!")
    return True


async def setup_database():
    """Set up the PostgreSQL database."""
    
    logger.info("Setting up database...")
    
    try:
        from app.database import init_db
        await init_db()
        logger.info("✅ Database initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        logger.error("Please ensure PostgreSQL is running and configured correctly")
        return False


def main():
    """Main function to run the application."""
    
    logger.info("🚀 Starting Product Search & Price Comparison Application")
    logger.info(f"Version: {settings.app_version}")
    logger.info(f"Environment: {'Development' if settings.debug else 'Production'}")
    
    # Check prerequisites
    if not check_prerequisites():
        logger.error("❌ Prerequisites not met. Exiting.")
        sys.exit(1)
    
    # Setup database
    try:
        asyncio.run(setup_database())
    except Exception as e:
        logger.error(f"❌ Database setup failed: {e}")
        sys.exit(1)
    
    # Start the application
    logger.info(f"🌐 Starting server on {settings.host}:{settings.port}")
    logger.info(f"📊 ChromaDB path: {settings.chroma_db_path}")
    logger.info(f"🤖 Embedding model: {settings.embedding_model_name}")
    
    try:
        uvicorn.run(
            "app.main:app",
            host=settings.host,
            port=settings.port,
            reload=settings.debug,
            log_level=settings.log_level.lower(),
            access_log=True
        )
    except KeyboardInterrupt:
        logger.info("👋 Application stopped by user")
    except Exception as e:
        logger.error(f"❌ Application failed to start: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
