"""
Wishlist API Endpoints
=====================

FastAPI endpoints for wishlist management functionality.
"""

from fastapi import APIRouter, Depends, HTTPException, Request, Header
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, func
from sqlalchemy.orm import selectinload
import logging
from typing import Optional, List
import uuid
from datetime import datetime

from app.database import get_db
from app.models.wishlist import WishlistItem
from app.models.user_session import UserSession
from app.schemas.wishlist import (
    AddToWishlistRequest, UpdateWishlistItemRequest, WishlistItemResponse,
    WishlistResponse, WishlistSummary, BulkWishlistOperation,
    WishlistOperationResponse, WishlistError
)
from app.services.price_comparison_service import PriceComparisonService
from app.services.search_service import SearchService

logger = logging.getLogger(__name__)
router = APIRouter()


async def get_or_create_session(
    db: AsyncSession,
    session_token: Optional[str] = None
) -> UserSession:
    """Get existing session or create a new one."""
    
    if session_token:
        # Try to find existing session
        result = await db.execute(
            select(UserSession).where(
                UserSession.session_token == session_token,
                UserSession.is_active == True,
                UserSession.expires_at > datetime.utcnow()
            )
        )
        session = result.scalar_one_or_none()
        
        if session:
            return session
    
    # Create new session
    from datetime import timedelta
    session = UserSession(
        session_token=str(uuid.uuid4()),
        expires_at=datetime.utcnow() + timedelta(hours=24),
        is_active=True
    )
    
    db.add(session)
    await db.commit()
    await db.refresh(session)
    
    return session


@router.post("/add", response_model=WishlistOperationResponse)
async def add_to_wishlist(
    item_request: AddToWishlistRequest,
    request: Request,
    db: AsyncSession = Depends(get_db),
    x_session_token: Optional[str] = Header(None)
):
    """
    Add a product to the user's wishlist.
    
    This endpoint adds a product to the wishlist, creating a new session
    if one doesn't exist. Duplicate products (same MPN) are updated rather
    than creating duplicates.
    """
    try:
        # Get or create user session
        session = await get_or_create_session(db, x_session_token)
        
        # Check if item already exists in wishlist
        existing_item_result = await db.execute(
            select(WishlistItem).where(
                WishlistItem.session_id == session.id,
                WishlistItem.mpn == item_request.mpn,
                WishlistItem.brand == item_request.brand
            )
        )
        existing_item = existing_item_result.scalar_one_or_none()
        
        if existing_item:
            # Update existing item
            existing_item.product_name = item_request.product_name
            existing_item.category = item_request.category
            existing_item.subcategory = item_request.subcategory
            existing_item.seller = item_request.seller
            existing_item.price = item_request.price
            existing_item.product_url = item_request.product_url
            existing_item.search_query = item_request.search_query
            existing_item.similarity_score = item_request.similarity_score
            existing_item.similarity_distance = item_request.similarity_distance
            existing_item.user_notes = item_request.user_notes
            existing_item.priority = item_request.priority or 0
            existing_item.updated_at = datetime.utcnow()
            
            await db.commit()
            
            return WishlistOperationResponse(
                success=True,
                message="Product updated in wishlist",
                affected_items=1,
                item_id=str(existing_item.id)
            )
        
        else:
            # Create new wishlist item
            wishlist_item = WishlistItem(
                session_id=session.id,
                product_id=item_request.product_id,
                mpn=item_request.mpn,
                product_name=item_request.product_name,
                brand=item_request.brand,
                category=item_request.category,
                subcategory=item_request.subcategory,
                seller=item_request.seller,
                price=item_request.price,
                price_currency=item_request.price_currency,
                product_url=item_request.product_url,
                search_query=item_request.search_query,
                similarity_score=item_request.similarity_score,
                similarity_distance=item_request.similarity_distance,
                user_notes=item_request.user_notes,
                priority=item_request.priority or 0
            )
            
            db.add(wishlist_item)
            
            # Update session interaction count
            session.wishlist_interactions += 1
            
            await db.commit()
            await db.refresh(wishlist_item)
            
            return WishlistOperationResponse(
                success=True,
                message="Product added to wishlist",
                affected_items=1,
                item_id=str(wishlist_item.id)
            )
    
    except Exception as e:
        logger.error(f"Error adding to wishlist: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=WishlistError(
                error=str(e),
                error_type="add_to_wishlist_error"
            ).dict()
        )


@router.get("/", response_model=WishlistResponse)
async def get_wishlist(
    request: Request,
    db: AsyncSession = Depends(get_db),
    x_session_token: Optional[str] = Header(None)
):
    """
    Get the user's complete wishlist.
    
    Returns all items in the user's wishlist with summary statistics.
    """
    try:
        if not x_session_token:
            # Return empty wishlist for new users
            return WishlistResponse(
                session_id="",
                items=[],
                total_items=0,
                total_brands=0,
                total_categories=0,
                last_updated=datetime.utcnow()
            )
        
        # Get user session
        session_result = await db.execute(
            select(UserSession).where(
                UserSession.session_token == x_session_token,
                UserSession.is_active == True
            )
        )
        session = session_result.scalar_one_or_none()
        
        if not session:
            return WishlistResponse(
                session_id="",
                items=[],
                total_items=0,
                total_brands=0,
                total_categories=0,
                last_updated=datetime.utcnow()
            )
        
        # Get wishlist items
        items_result = await db.execute(
            select(WishlistItem)
            .where(WishlistItem.session_id == session.id)
            .order_by(WishlistItem.priority.desc(), WishlistItem.added_at.desc())
        )
        items = items_result.scalars().all()
        
        # Convert to response format
        item_responses = [
            WishlistItemResponse(
                id=str(item.id),
                session_id=str(item.session_id),
                product_id=item.product_id,
                mpn=item.mpn,
                product_name=item.product_name,
                brand=item.brand,
                category=item.category,
                subcategory=item.subcategory,
                seller=item.seller,
                price=item.price,
                price_currency=item.price_currency,
                product_url=item.product_url,
                search_query=item.search_query,
                similarity_score=item.similarity_score,
                similarity_distance=item.similarity_distance,
                added_at=item.added_at,
                updated_at=item.updated_at,
                user_notes=item.user_notes,
                priority=item.priority,
                metadata=item.metadata
            )
            for item in items
        ]
        
        # Calculate statistics
        brands = set(item.brand for item in items if item.brand)
        categories = set(item.category for item in items if item.category)
        prices = [item.price for item in items if item.price is not None]
        
        price_range = None
        average_price = None
        if prices:
            price_range = {"min": min(prices), "max": max(prices)}
            average_price = sum(prices) / len(prices)
        
        last_updated = max((item.updated_at for item in items), default=datetime.utcnow())
        
        return WishlistResponse(
            session_id=str(session.id),
            items=item_responses,
            total_items=len(items),
            total_brands=len(brands),
            total_categories=len(categories),
            average_price=average_price,
            price_range=price_range,
            last_updated=last_updated
        )
    
    except Exception as e:
        logger.error(f"Error getting wishlist: {e}")
        raise HTTPException(
            status_code=500,
            detail=WishlistError(
                error=str(e),
                error_type="get_wishlist_error"
            ).dict()
        )


@router.delete("/{item_id}", response_model=WishlistOperationResponse)
async def remove_from_wishlist(
    item_id: str,
    request: Request,
    db: AsyncSession = Depends(get_db),
    x_session_token: Optional[str] = Header(None)
):
    """
    Remove a product from the wishlist.
    
    Removes the specified item from the user's wishlist.
    """
    try:
        if not x_session_token:
            raise HTTPException(status_code=401, detail="Session token required")
        
        # Get user session
        session_result = await db.execute(
            select(UserSession).where(
                UserSession.session_token == x_session_token,
                UserSession.is_active == True
            )
        )
        session = session_result.scalar_one_or_none()
        
        if not session:
            raise HTTPException(status_code=401, detail="Invalid session")
        
        # Find and delete the item
        item_result = await db.execute(
            select(WishlistItem).where(
                WishlistItem.id == uuid.UUID(item_id),
                WishlistItem.session_id == session.id
            )
        )
        item = item_result.scalar_one_or_none()
        
        if not item:
            raise HTTPException(status_code=404, detail="Item not found in wishlist")
        
        await db.delete(item)
        await db.commit()
        
        return WishlistOperationResponse(
            success=True,
            message="Product removed from wishlist",
            affected_items=1,
            item_id=item_id
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing from wishlist: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=WishlistError(
                error=str(e),
                error_type="remove_from_wishlist_error",
                item_id=item_id
            ).dict()
        )


@router.delete("/", response_model=WishlistOperationResponse)
async def clear_wishlist(
    request: Request,
    db: AsyncSession = Depends(get_db),
    x_session_token: Optional[str] = Header(None)
):
    """
    Clear all items from the wishlist.
    
    Removes all items from the user's wishlist.
    """
    try:
        if not x_session_token:
            raise HTTPException(status_code=401, detail="Session token required")
        
        # Get user session
        session_result = await db.execute(
            select(UserSession).where(
                UserSession.session_token == x_session_token,
                UserSession.is_active == True
            )
        )
        session = session_result.scalar_one_or_none()
        
        if not session:
            raise HTTPException(status_code=401, detail="Invalid session")
        
        # Count items before deletion
        count_result = await db.execute(
            select(func.count(WishlistItem.id)).where(
                WishlistItem.session_id == session.id
            )
        )
        item_count = count_result.scalar()
        
        # Delete all items
        await db.execute(
            delete(WishlistItem).where(WishlistItem.session_id == session.id)
        )
        await db.commit()
        
        return WishlistOperationResponse(
            success=True,
            message=f"Cleared {item_count} items from wishlist",
            affected_items=item_count
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error clearing wishlist: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=WishlistError(
                error=str(e),
                error_type="clear_wishlist_error"
            ).dict()
        )


@router.get("/summary", response_model=WishlistSummary)
async def get_wishlist_summary(
    request: Request,
    db: AsyncSession = Depends(get_db),
    x_session_token: Optional[str] = Header(None)
):
    """
    Get a summary of the user's wishlist.
    
    Returns summary statistics and recent additions.
    """
    try:
        if not x_session_token:
            return WishlistSummary(
                total_items=0,
                brands=[],
                categories=[],
                recent_additions=[]
            )
        
        # Get user session
        session_result = await db.execute(
            select(UserSession).where(
                UserSession.session_token == x_session_token,
                UserSession.is_active == True
            )
        )
        session = session_result.scalar_one_or_none()
        
        if not session:
            return WishlistSummary(
                total_items=0,
                brands=[],
                categories=[],
                recent_additions=[]
            )
        
        # Get wishlist items
        items_result = await db.execute(
            select(WishlistItem)
            .where(WishlistItem.session_id == session.id)
            .order_by(WishlistItem.added_at.desc())
        )
        items = items_result.scalars().all()
        
        # Calculate summary statistics
        brands = list(set(item.brand for item in items if item.brand))
        categories = list(set(item.category for item in items if item.category))
        
        # Price statistics
        prices = [item.price for item in items if item.price is not None]
        price_stats = None
        if prices:
            price_stats = {
                "min": min(prices),
                "max": max(prices),
                "average": sum(prices) / len(prices),
                "count": len(prices)
            }
        
        # Recent additions (last 5)
        recent_items = [
            WishlistItemResponse(
                id=str(item.id),
                session_id=str(item.session_id),
                product_id=item.product_id,
                mpn=item.mpn,
                product_name=item.product_name,
                brand=item.brand,
                category=item.category,
                subcategory=item.subcategory,
                seller=item.seller,
                price=item.price,
                price_currency=item.price_currency,
                product_url=item.product_url,
                search_query=item.search_query,
                similarity_score=item.similarity_score,
                similarity_distance=item.similarity_distance,
                added_at=item.added_at,
                updated_at=item.updated_at,
                user_notes=item.user_notes,
                priority=item.priority,
                metadata=item.metadata
            )
            for item in items[:5]
        ]
        
        return WishlistSummary(
            total_items=len(items),
            brands=brands,
            categories=categories,
            price_stats=price_stats,
            recent_additions=recent_items
        )
    
    except Exception as e:
        logger.error(f"Error getting wishlist summary: {e}")
        raise HTTPException(
            status_code=500,
            detail=WishlistError(
                error=str(e),
                error_type="get_wishlist_summary_error"
            ).dict()
        )


@router.post("/compare-prices")
async def compare_wishlist_prices(
    request: Request,
    db: AsyncSession = Depends(get_db),
    x_session_token: Optional[str] = Header(None),
    similarity_threshold: float = 0.3,
    max_alternatives: int = 10
):
    """
    Compare prices for all items in the wishlist.

    This endpoint finds alternative products for each wishlist item
    and provides price comparison data.
    """
    try:
        if not x_session_token:
            raise HTTPException(status_code=401, detail="Session token required")

        # Get user session
        session_result = await db.execute(
            select(UserSession).where(
                UserSession.session_token == x_session_token,
                UserSession.is_active == True
            )
        )
        session = session_result.scalar_one_or_none()

        if not session:
            raise HTTPException(status_code=401, detail="Invalid session")

        # Get wishlist items
        items_result = await db.execute(
            select(WishlistItem).where(WishlistItem.session_id == session.id)
        )
        wishlist_items = items_result.scalars().all()

        if not wishlist_items:
            return {
                "comparison_results": [],
                "summary": {
                    "total_items_compared": 0,
                    "items_with_alternatives": 0,
                    "potential_total_savings": 0.0
                },
                "message": "No items in wishlist to compare"
            }

        # Get search service from app state
        search_service = request.app.state.search_service

        # Initialize price comparison service
        price_service = PriceComparisonService(search_service)

        # Perform price comparison
        comparison_results = await price_service.compare_wishlist_prices(
            wishlist_items=wishlist_items,
            similarity_threshold=similarity_threshold,
            max_alternatives_per_item=max_alternatives
        )

        # Update session analytics
        session.price_comparisons += 1
        await db.commit()

        return comparison_results

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error comparing wishlist prices: {e}")
        raise HTTPException(
            status_code=500,
            detail=WishlistError(
                error=str(e),
                error_type="price_comparison_error"
            ).dict()
        )
