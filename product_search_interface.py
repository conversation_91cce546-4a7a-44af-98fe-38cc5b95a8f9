#!/usr/bin/env python3
"""
Product Search Interface
========================

This script provides a user-friendly interface for product search with:
1. Real-time product search
2. MPN-based product grouping
3. Cross-brand similarity detection
4. Clear result categorization
"""

import logging
from typing import List, Dict, Any, Tu<PERSON>
from advanced_product_search import AdvancedProductSearcher
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProductSearchInterface:
    """User-friendly interface for product search functionality."""
    
    def __init__(self):
        """Initialize the search interface."""
        self.searcher = AdvancedProductSearcher()
        self.is_initialized = False
        
    def initialize(self):
        """Initialize the search system."""
        if self.is_initialized:
            return
            
        print("🚀 Initializing Product Search System...")
        print("Loading embedding model...")
        self.searcher.load_embedding_model()
        
        print("Connecting to product database...")
        self.searcher.connect_to_database()
        
        self.is_initialized = True
        print("✅ Search system ready!")
        print(f"📊 Database contains {self.searcher.collection.count():,} products")
        print("=" * 60)
    
    def search_products(self, query: str, max_results: int = 15) -> Dict[str, Any]:
        """
        Comprehensive product search with categorized results.
        
        Args:
            query: Search query
            max_results: Maximum number of results per category
            
        Returns:
            Categorized search results
        """
        if not self.is_initialized:
            self.initialize()
        
        # Perform cross-brand similarity search
        similarity_results = self.searcher.cross_brand_similarity_search(
            query, 
            similarity_threshold=0.3, 
            max_results=max_results * 2  # Get more results for better categorization
        )
        
        # Categorize results
        exact_matches = []
        high_confidence_similar = []
        medium_confidence_similar = []
        category_related = []
        
        for match in similarity_results['all_matches']:
            distance = match['similarity_distance']
            score = match['similarity_score']
            
            # Check if query matches product name or MPN exactly
            query_lower = query.lower()
            name_lower = match['name'].lower()
            mpn_lower = match['mpn'].lower()
            
            if (query_lower in name_lower or query_lower in mpn_lower or 
                name_lower in query_lower or mpn_lower in query_lower):
                exact_matches.append(match)
            elif distance <= 0.3:  # High confidence threshold
                high_confidence_similar.append(match)
            elif distance <= 0.5:  # Medium confidence threshold
                medium_confidence_similar.append(match)
            else:
                category_related.append(match)
        
        return {
            'query': query,
            'exact_matches': exact_matches[:max_results],
            'high_confidence_similar': high_confidence_similar[:max_results],
            'medium_confidence_similar': medium_confidence_similar[:max_results],
            'category_related': category_related[:max_results],
            'total_results': len(similarity_results['all_matches']),
            'brand_distribution': similarity_results['brand_distribution']
        }
    
    def get_mpn_variants(self, mpn: str) -> Dict[str, Any]:
        """Get all variants for a specific MPN with brand grouping."""
        if not self.is_initialized:
            self.initialize()
            
        return self.searcher.exact_mpn_search_with_brand_similarity(mpn)
    
    def format_search_results(self, results: Dict[str, Any]) -> str:
        """Format search results for display."""
        output = []
        
        output.append(f"🔍 SEARCH RESULTS FOR: '{results['query']}'")
        output.append(f"📊 Total Results: {results['total_results']}")
        output.append("=" * 80)
        
        # Exact matches
        if results['exact_matches']:
            output.append(f"\n🎯 EXACT MATCHES ({len(results['exact_matches'])} products)")
            output.append("These products directly match your search query")
            output.append("-" * 60)
            
            for i, match in enumerate(results['exact_matches'], 1):
                output.append(f"  {i}. {match['name']}")
                output.append(f"     🏷️  Brand: {match['brand']} | MPN: {match['mpn']}")
                output.append(f"     📂 Category: {match['category']} > {match['subcategory']}")
                output.append(f"     🏪 Seller: {match['seller']}")
                output.append(f"     📊 Similarity: {match['similarity_score']:.3f}")
                if match['url']:
                    output.append(f"     🔗 URL: {match['url']}")
                output.append("")
        
        # High confidence similar products
        if results['high_confidence_similar']:
            output.append(f"\n🔥 HIGH-CONFIDENCE SIMILAR PRODUCTS ({len(results['high_confidence_similar'])} products)")
            output.append("These products are very likely the same or equivalent items from different brands")
            output.append("-" * 60)
            
            for i, match in enumerate(results['high_confidence_similar'], 1):
                output.append(f"  {i}. {match['name']}")
                output.append(f"     🏷️  Brand: {match['brand']} | MPN: {match['mpn']}")
                output.append(f"     📂 Category: {match['category']} > {match['subcategory']}")
                output.append(f"     🏪 Seller: {match['seller']}")
                output.append(f"     📊 Similarity: {match['similarity_score']:.3f} (Distance: {match['similarity_distance']:.3f})")
                if match['url']:
                    output.append(f"     🔗 URL: {match['url']}")
                output.append("")
        
        # Medium confidence similar products
        if results['medium_confidence_similar']:
            output.append(f"\n⚡ SIMILAR PRODUCTS ({len(results['medium_confidence_similar'])} products)")
            output.append("These products are related and may be suitable alternatives")
            output.append("-" * 60)
            
            for i, match in enumerate(results['medium_confidence_similar'][:5], 1):  # Show top 5
                output.append(f"  {i}. {match['name']}")
                output.append(f"     🏷️  Brand: {match['brand']} | MPN: {match['mpn']}")
                output.append(f"     📂 Category: {match['category']} > {match['subcategory']}")
                output.append(f"     📊 Similarity: {match['similarity_score']:.3f}")
                output.append("")
        
        # Category related products (show fewer)
        if results['category_related']:
            output.append(f"\n📂 CATEGORY-RELATED PRODUCTS ({len(results['category_related'])} products)")
            output.append("These products are in the same category but may not be direct alternatives")
            output.append("-" * 60)
            
            for i, match in enumerate(results['category_related'][:3], 1):  # Show top 3
                output.append(f"  {i}. {match['name']}")
                output.append(f"     🏷️  Brand: {match['brand']} | MPN: {match['mpn']}")
                output.append(f"     📂 Category: {match['category']}")
                output.append(f"     📊 Similarity: {match['similarity_score']:.3f}")
                output.append("")
        
        # Brand distribution summary
        if results['brand_distribution']['total_brands'] > 1:
            output.append(f"\n🏷️  BRAND SUMMARY ({results['brand_distribution']['total_brands']} brands found)")
            output.append("-" * 60)
            
            top_brands = list(results['brand_distribution']['brand_stats'].items())[:5]
            for brand, stats in top_brands:
                output.append(f"  • {brand}: {stats['count']} products (Avg similarity: {stats['avg_similarity']:.3f})")
        
        return "\n".join(output)
    
    def interactive_search(self):
        """Run interactive search session."""
        if not self.is_initialized:
            self.initialize()
        
        print("\n🔍 INTERACTIVE PRODUCT SEARCH")
        print("Type your search queries below. Type 'quit' to exit.")
        print("Examples: 'Millennium Heat Cure', 'dental acrylic', 'MPN:6010002'")
        print("=" * 60)
        
        while True:
            try:
                query = input("\n🔍 Search: ").strip()
                
                if query.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                if not query:
                    continue
                
                # Check if it's an MPN search
                if query.upper().startswith('MPN:'):
                    mpn = query[4:].strip()
                    print(f"\n🔍 Searching for MPN: {mpn}")
                    mpn_results = self.get_mpn_variants(mpn)
                    formatted_results = self.searcher.format_mpn_search_results(mpn_results)
                    print(formatted_results)
                else:
                    # Regular product search
                    print(f"\n🔍 Searching for: {query}")
                    search_results = self.search_products(query)
                    formatted_results = self.format_search_results(search_results)
                    print(formatted_results)
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                continue


def main():
    """Main function to run the search interface."""
    interface = ProductSearchInterface()
    
    # Run some demonstration searches first
    print("🚀 PRODUCT SEARCH DEMONSTRATION")
    print("=" * 80)
    
    demo_queries = [
        "Millennium Heat Cure",
        "dental acrylic resin",
        "orthodontic retainer"
    ]
    
    interface.initialize()
    
    for query in demo_queries:
        print(f"\n{'='*60}")
        print(f"DEMO SEARCH: {query}")
        print('='*60)
        
        results = interface.search_products(query, max_results=5)
        formatted = interface.format_search_results(results)
        print(formatted)
    
    # Show MPN search example
    print(f"\n{'='*60}")
    print("DEMO MPN SEARCH: 6010002")
    print('='*60)
    
    mpn_results = interface.get_mpn_variants("6010002")
    formatted_mpn = interface.searcher.format_mpn_search_results(mpn_results)
    print(formatted_mpn)
    
    # Start interactive session
    print(f"\n{'='*60}")
    interface.interactive_search()


if __name__ == "__main__":
    main()
