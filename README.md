# ChromaDB Product Search System

A comprehensive product search and similarity detection system built with ChromaDB and state-of-the-art embeddings for e-commerce product data.

## 🚀 Features

### 1. **Advanced Embedding System**
- **Model**: BAAI/bge-large-en-v1.5 (1024 dimensions) - Top performer on MTEB leaderboard
- **Database**: ChromaDB with 219,796 embedded products
- **Document Format**: Semantically rich concatenation of `name | brand | maincat | subcat`
- **Unique Identifiers**: MPN (Manufacturer Part Number) based indexing

### 2. **Exact MPN Matching with Brand Similarity**
- Retrieves all product variants for a specific MPN
- Groups variants by brand with similarity scoring
- Ranks brands by semantic similarity to identify related manufacturers
- **Example**: MPN "6010002" shows variants from "Keystone Dental" and "National Keystone Group"

### 3. **Cross-Brand Product Similarity Detection**
- Semantic similarity search to find equivalent products across different brands
- Uses embedding distance thresholds (< 0.3 for high confidence matches)
- Distinguishes between truly similar products vs. category-only matches
- **Example**: "Millennium Heat Cure" finds similar acrylic products from multiple brands

### 4. **Intelligent Result Categorization**
- **Exact Matches**: Direct query matches in product name or MPN
- **High-Confidence Similar**: Distance ≤ 0.3 (likely same/equivalent products)
- **Similar Products**: Distance ≤ 0.5 (related alternatives)
- **Category-Related**: Same category but different products

### 5. **Real-Time Search Interface**
- Interactive command-line interface
- Support for both text queries and MPN searches (`MPN:6010002`)
- Live result formatting with similarity scores
- Brand distribution analysis

## 📁 Files Structure

```
├── product_embeddings.py          # Main embedding generation script
├── advanced_product_search.py     # Core search functionality
├── product_search_interface.py    # User-friendly search interface
├── test_model_compatibility.py    # Model compatibility testing
├── test_embeddings.py             # Small-scale testing utilities
└── all.json                       # Source product data
```

## 🛠️ Installation

```bash
pip install chromadb sentence-transformers numpy
```

## 🚀 Quick Start

### 1. Generate Embeddings (One-time setup)
```bash
python product_embeddings.py
```

### 2. Run Interactive Search
```bash
python product_search_interface.py
```

### 3. Test Advanced Features
```bash
python advanced_product_search.py
```

## 💡 Usage Examples

### Text Search
```
🔍 Search: Millennium Heat Cure
```
**Results**: 
- Exact matches for "Millennium Heat Cure" products
- High-confidence similar products from different brands
- Brand similarity analysis

### MPN Search
```
🔍 Search: MPN:6010002
```
**Results**:
- All variants with MPN "6010002"
- Grouped by brand (Keystone Dental, National Keystone Group)
- Brand similarity scoring

### Cross-Brand Detection
```
🔍 Search: dental acrylic resin
```
**Results**:
- Products from multiple brands with high semantic similarity
- Similarity scores to distinguish quality of matches
- Brand distribution analysis

## 🎯 Key Technical Achievements

### 1. **Dimension Compatibility**
- Consistent use of BAAI/bge-large-en-v1.5 (1024 dimensions) for both indexing and querying
- Proper embedding generation and storage in ChromaDB

### 2. **Semantic Understanding**
- Successfully distinguishes between:
  - Truly similar products (e.g., same acrylic resin from different brands)
  - Category-only matches (e.g., different dental products in same category)

### 3. **Brand Relationship Detection**
- Identifies related brands (e.g., "Keystone Dental" and "National Keystone Group")
- Calculates brand similarity scores using embedding distances

### 4. **Robust Data Processing**
- Handles missing/null values in product data
- Processes 219,796 products with comprehensive error handling
- Maintains data integrity with proper metadata storage

## 📊 Performance Metrics

- **Database Size**: 219,796 products
- **Embedding Model**: BAAI/bge-large-en-v1.5 (state-of-the-art)
- **Search Speed**: Sub-second response times
- **Accuracy**: High-precision similarity detection with configurable thresholds

## 🔍 Search Result Categories

### Exact Matches (Similarity > 0.8)
Products that directly match the search query in name or MPN.

### High-Confidence Similar (Distance ≤ 0.3)
Products that are very likely the same or equivalent items from different manufacturers.

### Similar Products (Distance ≤ 0.5)
Related products that may serve as suitable alternatives.

### Category-Related (Distance > 0.5)
Products in the same category but not direct alternatives.

## 🏷️ Brand Analysis Features

- **Brand Grouping**: Automatic grouping of product variants by brand
- **Similarity Scoring**: Quantitative brand relationship analysis
- **Distribution Analysis**: Statistical breakdown of search results by brand

## 🧪 Testing & Validation

The system includes comprehensive testing with specific examples:

- **MPN "6010002"**: Demonstrates brand grouping with Keystone variants
- **"Millennium Heat Cure"**: Shows cross-brand similarity detection
- **"dental acrylic resin"**: Validates semantic understanding vs. keyword matching
- **"orthodontic retainer"**: Tests category-specific product matching

## 🔧 Configuration Options

- **Similarity Threshold**: Adjustable distance threshold for high-confidence matches
- **Result Limits**: Configurable number of results per category
- **Model Selection**: Support for different embedding models
- **Database Path**: Customizable ChromaDB storage location

## 📈 Future Enhancements

- Web-based interface for easier interaction
- API endpoints for integration with e-commerce platforms
- Advanced filtering by category, price range, and seller
- Machine learning-based relevance ranking
- Real-time product data updates

---

**Built with**: ChromaDB, Sentence Transformers, BAAI/bge-large-en-v1.5, Python 3.13
