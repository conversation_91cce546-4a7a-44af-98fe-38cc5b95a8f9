# Product Search & Price Comparison Web Application

A comprehensive web application for product search and price comparison built with FastAPI, ChromaDB, and state-of-the-art embeddings. Features real-time semantic search, intelligent wishlist management, and cross-brand price comparison.

## 🚀 Features

### 🔍 **Advanced Search System**
- **Real-time semantic search** with autocomplete suggestions
- **Cross-brand similarity detection** using state-of-the-art embeddings
- **MPN-based product grouping** with brand similarity scoring
- **Intelligent result categorization** (exact matches, similar products, category-related)

### 🛒 **Smart Wishlist Management**
- **Interactive wishlist** with persistent storage
- **Session-based user management** (no authentication required)
- **Product metadata tracking** with search context
- **Bulk operations** (add, remove, clear)

### 💰 **Price Comparison Engine**
- **Automatic alternative detection** using MPN matching and semantic similarity
- **Cross-seller price comparison** with savings calculation
- **Confidence scoring** for product matches
- **Visual comparison cards** with actionable insights

### 🎨 **Modern Web Interface**
- **Responsive design** optimized for desktop and mobile
- **Real-time search suggestions** with debounced input
- **Interactive product cards** with clickable links
- **Loading states and error handling** for smooth UX

### 🏗️ **Robust Architecture**
- **FastAPI backend** with async endpoints and automatic API documentation
- **PostgreSQL database** for user sessions, wishlist data, and analytics
- **ChromaDB vector database** with 219,796 embedded products
- **BAAI/bge-large-en-v1.5 embeddings** (1024 dimensions) - Top MTEB performer

### 2. **Exact MPN Matching with Brand Similarity**
- Retrieves all product variants for a specific MPN
- Groups variants by brand with similarity scoring
- Ranks brands by semantic similarity to identify related manufacturers
- **Example**: MPN "6010002" shows variants from "Keystone Dental" and "National Keystone Group"

### 3. **Cross-Brand Product Similarity Detection**
- Semantic similarity search to find equivalent products across different brands
- Uses embedding distance thresholds (< 0.3 for high confidence matches)
- Distinguishes between truly similar products vs. category-only matches
- **Example**: "Millennium Heat Cure" finds similar acrylic products from multiple brands

### 4. **Intelligent Result Categorization**
- **Exact Matches**: Direct query matches in product name or MPN
- **High-Confidence Similar**: Distance ≤ 0.3 (likely same/equivalent products)
- **Similar Products**: Distance ≤ 0.5 (related alternatives)
- **Category-Related**: Same category but different products

### 5. **Real-Time Search Interface**
- Interactive command-line interface
- Support for both text queries and MPN searches (`MPN:6010002`)
- Live result formatting with similarity scores
- Brand distribution analysis

## 📁 Project Structure

```
├── app/                           # FastAPI application
│   ├── api/                       # API endpoints
│   │   ├── search.py             # Search endpoints
│   │   ├── wishlist.py           # Wishlist management
│   │   ├── analytics.py          # Analytics endpoints
│   │   └── session.py            # Session management
│   ├── models/                    # SQLAlchemy models
│   │   ├── user_session.py       # User session model
│   │   ├── wishlist.py           # Wishlist item model
│   │   ├── search_history.py     # Search analytics model
│   │   └── product_analytics.py  # Product popularity model
│   ├── schemas/                   # Pydantic schemas
│   │   ├── search.py             # Search request/response schemas
│   │   └── wishlist.py           # Wishlist schemas
│   ├── services/                  # Business logic services
│   │   ├── search_service.py     # Search service integration
│   │   └── price_comparison_service.py # Price comparison logic
│   ├── config.py                 # Application configuration
│   ├── database.py               # Database setup and session management
│   └── main.py                   # FastAPI application entry point
├── static/                        # Frontend assets
│   ├── css/main.css              # Application styles
│   └── js/main.js                # Frontend JavaScript
├── alembic/                       # Database migrations
├── product_embeddings.py         # ChromaDB embedding generation
├── advanced_product_search.py    # Core search functionality
├── run_app.py                    # Application runner
├── setup_database.py            # Database setup script
├── test_app.py                   # Comprehensive testing suite
└── all.json                      # Source product data (219,796 products)
```

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.8+
- PostgreSQL 12+
- 8GB+ RAM (for embedding model)
- 2GB+ disk space (for ChromaDB)

### 1. Clone and Install Dependencies
```bash
git clone <repository-url>
cd product-search-app
pip install -r requirements.txt
```

### 2. Configure Environment
```bash
cp .env.example .env
# Edit .env with your database credentials and settings
```

### 3. Set Up PostgreSQL Database
```bash
# Create database
createdb product_search

# Run database setup
python setup_database.py
```

### 4. Generate Product Embeddings (One-time setup)
```bash
# This processes 219,796 products and takes ~30-60 minutes
python product_embeddings.py
```

### 5. Start the Application
```bash
python run_app.py
```

### 6. Access the Application
- **Web Interface**: http://localhost:8000
- **API Documentation**: http://localhost:8000/api/docs
- **Health Check**: http://localhost:8000/health

## 🧪 Testing

### Run Comprehensive Tests
```bash
# Start the application first, then in another terminal:
python test_app.py
```

### Test Individual Components
```bash
# Test search functionality
python advanced_product_search.py

# Test model compatibility
python test_model_compatibility.py
```

## 💡 Usage Examples

### Text Search
```
🔍 Search: Millennium Heat Cure
```
**Results**: 
- Exact matches for "Millennium Heat Cure" products
- High-confidence similar products from different brands
- Brand similarity analysis

### MPN Search
```
🔍 Search: MPN:6010002
```
**Results**:
- All variants with MPN "6010002"
- Grouped by brand (Keystone Dental, National Keystone Group)
- Brand similarity scoring

### Cross-Brand Detection
```
🔍 Search: dental acrylic resin
```
**Results**:
- Products from multiple brands with high semantic similarity
- Similarity scores to distinguish quality of matches
- Brand distribution analysis

## 🎯 Key Technical Achievements

### 1. **Dimension Compatibility**
- Consistent use of BAAI/bge-large-en-v1.5 (1024 dimensions) for both indexing and querying
- Proper embedding generation and storage in ChromaDB

### 2. **Semantic Understanding**
- Successfully distinguishes between:
  - Truly similar products (e.g., same acrylic resin from different brands)
  - Category-only matches (e.g., different dental products in same category)

### 3. **Brand Relationship Detection**
- Identifies related brands (e.g., "Keystone Dental" and "National Keystone Group")
- Calculates brand similarity scores using embedding distances

### 4. **Robust Data Processing**
- Handles missing/null values in product data
- Processes 219,796 products with comprehensive error handling
- Maintains data integrity with proper metadata storage

## 📊 Performance Metrics

- **Database Size**: 219,796 products
- **Embedding Model**: BAAI/bge-large-en-v1.5 (state-of-the-art)
- **Search Speed**: Sub-second response times
- **Accuracy**: High-precision similarity detection with configurable thresholds

## 🔍 Search Result Categories

### Exact Matches (Similarity > 0.8)
Products that directly match the search query in name or MPN.

### High-Confidence Similar (Distance ≤ 0.3)
Products that are very likely the same or equivalent items from different manufacturers.

### Similar Products (Distance ≤ 0.5)
Related products that may serve as suitable alternatives.

### Category-Related (Distance > 0.5)
Products in the same category but not direct alternatives.

## 🏷️ Brand Analysis Features

- **Brand Grouping**: Automatic grouping of product variants by brand
- **Similarity Scoring**: Quantitative brand relationship analysis
- **Distribution Analysis**: Statistical breakdown of search results by brand

## 🧪 Testing & Validation

The system includes comprehensive testing with specific examples:

- **MPN "6010002"**: Demonstrates brand grouping with Keystone variants
- **"Millennium Heat Cure"**: Shows cross-brand similarity detection
- **"dental acrylic resin"**: Validates semantic understanding vs. keyword matching
- **"orthodontic retainer"**: Tests category-specific product matching

## 🔧 Configuration Options

- **Similarity Threshold**: Adjustable distance threshold for high-confidence matches
- **Result Limits**: Configurable number of results per category
- **Model Selection**: Support for different embedding models
- **Database Path**: Customizable ChromaDB storage location

## 📈 Future Enhancements

- Web-based interface for easier interaction
- API endpoints for integration with e-commerce platforms
- Advanced filtering by category, price range, and seller
- Machine learning-based relevance ranking
- Real-time product data updates

---

**Built with**: ChromaDB, Sentence Transformers, BAAI/bge-large-en-v1.5, Python 3.13
