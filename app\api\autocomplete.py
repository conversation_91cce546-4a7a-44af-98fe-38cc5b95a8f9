"""
Fast Autocomplete API Endpoints
===============================

High-performance autocomplete endpoints for instant product suggestions.
"""

from fastapi import APIRouter, Query, HTTPException
from typing import List, Dict, Any
import logging
import time

from app.services.autocomplete_service import get_autocomplete_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/suggestions")
async def get_autocomplete_suggestions(
    q: str = Query(..., min_length=1, max_length=100, description="Search query"),
    limit: int = Query(default=8, ge=1, le=15, description="Maximum number of suggestions")
) -> Dict[str, Any]:
    """
    Get fast autocomplete suggestions.
    
    This endpoint provides sub-100ms response times for product suggestions
    using optimized in-memory indices.
    
    **Performance targets:**
    - Response time: < 100ms
    - Triggers after 2+ characters
    - Returns 5-10 most relevant matches
    
    **Match types:**
    - `mpn_exact`: Exact MPN match (highest priority)
    - `name_prefix`: Product name starts with query
    - `name_contains`: Product name contains query word
    - `brand`: Brand name matches
    - `fuzzy`: Partial/fuzzy match
    - `popular`: Popular products (for short queries)
    """
    start_time = time.time()
    
    try:
        # Get autocomplete service
        autocomplete_service = await get_autocomplete_service()
        
        # Get suggestions
        suggestions = await autocomplete_service.get_suggestions(q, limit)
        
        # Calculate response time
        response_time_ms = (time.time() - start_time) * 1000
        
        return {
            "query": q,
            "suggestions": suggestions,
            "count": len(suggestions),
            "response_time_ms": round(response_time_ms, 2),
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Autocomplete error for query '{q}': {e}")
        response_time_ms = (time.time() - start_time) * 1000
        
        return {
            "query": q,
            "suggestions": [],
            "count": 0,
            "response_time_ms": round(response_time_ms, 2),
            "status": "error",
            "error": "Autocomplete service temporarily unavailable"
        }


@router.get("/popular")
async def get_popular_products(
    limit: int = Query(default=10, ge=1, le=20, description="Number of popular products")
) -> Dict[str, Any]:
    """
    Get popular products for default suggestions.
    
    Returns a list of popular products that can be shown
    when the user hasn't typed anything yet.
    """
    start_time = time.time()
    
    try:
        # Get autocomplete service
        autocomplete_service = await get_autocomplete_service()
        
        # Get popular products (empty query returns popular items)
        suggestions = await autocomplete_service.get_suggestions("", limit)
        
        response_time_ms = (time.time() - start_time) * 1000
        
        return {
            "popular_products": suggestions,
            "count": len(suggestions),
            "response_time_ms": round(response_time_ms, 2),
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Popular products error: {e}")
        response_time_ms = (time.time() - start_time) * 1000
        
        return {
            "popular_products": [],
            "count": 0,
            "response_time_ms": round(response_time_ms, 2),
            "status": "error",
            "error": "Popular products service temporarily unavailable"
        }


@router.get("/search/{query}")
async def quick_search(
    query: str,
    limit: int = Query(default=5, ge=1, le=10, description="Number of results")
) -> Dict[str, Any]:
    """
    Quick search endpoint for immediate results.
    
    This is a lightweight search that returns instant results
    without the full semantic search processing.
    """
    start_time = time.time()
    
    try:
        # Get autocomplete service
        autocomplete_service = await get_autocomplete_service()
        
        # Get suggestions
        suggestions = await autocomplete_service.get_suggestions(query, limit)
        
        # Format as search results
        results = []
        for suggestion in suggestions:
            results.append({
                "product_id": suggestion["product_id"],
                "name": suggestion["text"],
                "brand": suggestion["brand"],
                "mpn": suggestion["mpn"],
                "category": suggestion["category"],
                "match_type": suggestion["match_type"],
                "product_url": suggestion.get("url", ""),
                "similarity_score": 1.0 if suggestion["match_type"] == "mpn_exact" else 0.8
            })
        
        response_time_ms = (time.time() - start_time) * 1000
        
        return {
            "query": query,
            "results": results,
            "total_results": len(results),
            "response_time_ms": round(response_time_ms, 2),
            "search_type": "quick_search",
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Quick search error for '{query}': {e}")
        response_time_ms = (time.time() - start_time) * 1000
        
        return {
            "query": query,
            "results": [],
            "total_results": 0,
            "response_time_ms": round(response_time_ms, 2),
            "search_type": "quick_search",
            "status": "error",
            "error": "Quick search service temporarily unavailable"
        }


@router.get("/health")
async def autocomplete_health_check():
    """Health check for autocomplete service."""
    try:
        autocomplete_service = await get_autocomplete_service()
        
        # Test with a simple query
        test_suggestions = await autocomplete_service.get_suggestions("test", 1)
        
        return {
            "status": "healthy",
            "service": "autocomplete",
            "initialized": autocomplete_service.is_initialized,
            "test_query_results": len(test_suggestions)
        }
        
    except Exception as e:
        logger.error(f"Autocomplete health check failed: {e}")
        return {
            "status": "unhealthy",
            "service": "autocomplete",
            "error": str(e)
        }
