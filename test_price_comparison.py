#!/usr/bin/env python3
"""
Test Price Comparison Service
=============================

Test the price comparison service directly to verify functionality.
"""

import asyncio
import logging
from app.services.price_comparison_service import PriceComparisonService
from app.models.wishlist import WishlistItem
from datetime import datetime
import uuid

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_price_comparison():
    """Test the price comparison service."""
    logger.info("🧪 Testing Price Comparison Service")
    
    # Create a mock wishlist item
    test_item = WishlistItem(
        id=str(uuid.uuid4()),
        session_id="test-session",
        product_id="166161",
        mpn="DENTAL LT COMFORT RESIN",
        product_name="Formlabs Resin 5L",
        brand="Formlabs",
        category="Laboratory",
        seller="Henry Schein",
        product_url="https://www.henryschein.com/test",
        price_currency="USD",
        item_metadata={}
    )
    
    # Initialize service
    service = PriceComparisonService()
    
    try:
        logger.info(f"🔍 Testing price comparison for: {test_item.product_name}")
        
        # Test single item comparison
        result = await service._compare_single_item(
            test_item, 
            similarity_threshold=0.3, 
            max_alternatives=5
        )
        
        logger.info(f"✅ Comparison completed successfully!")
        logger.info(f"📊 Found {result['total_alternatives_found']} alternatives")
        logger.info(f"🎯 MPN matches: {len(result['mpn_matches'])}")
        logger.info(f"🔗 Similar products: {len(result['similar_products'])}")
        
        if result['alternatives']:
            logger.info("🏆 Top alternatives:")
            for i, alt in enumerate(result['alternatives'][:3], 1):
                logger.info(f"  {i}. {alt['name']} ({alt['brand']}) - {alt['type']}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ Error testing price comparison: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(test_price_comparison())
