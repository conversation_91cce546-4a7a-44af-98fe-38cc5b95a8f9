#!/usr/bin/env python3
"""
Test Product Embeddings - Small Scale Test
==========================================

This script tests the embedding process with a small subset of data to identify and fix issues.
"""

import json
import logging
from typing import List, Dict, Any
from product_embeddings import ProductEmbeddingProcessor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_small_dataset():
    """Test with a small subset of the data to identify issues."""
    
    # Load just the first 1000 entries for testing
    logger.info("Loading small dataset for testing...")
    
    try:
        with open("all.json", 'r', encoding='utf-8') as f:
            full_data = json.load(f)
        
        # Take first 1000 entries for testing
        test_data = full_data[:1000]
        logger.info(f"Testing with {len(test_data)} entries")
        
        # Initialize processor with a smaller, faster model for testing
        processor = ProductEmbeddingProcessor(
            model_name="all-MiniLM-L6-v2",  # Smaller model for faster testing
            db_path="./test_chroma_db"
        )
        
        # Load embedding model
        processor.load_embedding_model()
        
        # Initialize ChromaDB (reset collection for clean test)
        processor.initialize_chromadb(collection_name="test_product_embeddings", reset_collection=True)
        
        # Process data
        processed_products = processor.process_products_for_embedding(test_data)
        
        if processed_products:
            logger.info(f"Successfully processed {len(processed_products)} products")
            
            # Generate embeddings for a small batch first
            test_batch = processed_products[:10]  # Just 10 products for initial test
            processor.generate_embeddings_and_store(test_batch, batch_size=5)
            
            # Get collection info
            info = processor.get_collection_info()
            logger.info(f"Test collection info: {info}")
            
            logger.info("Small scale test completed successfully!")
            return True
        else:
            logger.error("No products were processed successfully")
            return False
            
    except Exception as e:
        logger.error(f"Error in test: {e}")
        import traceback
        traceback.print_exc()
        return False

def inspect_problematic_entries():
    """Inspect entries that might be causing issues."""
    
    logger.info("Inspecting potentially problematic entries...")
    
    try:
        with open("all.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        problematic_entries = []
        
        for idx, entry in enumerate(data[:50000]):  # Check first 50k entries
            if entry is None:
                problematic_entries.append(f"Entry {idx}: None entry")
                continue
                
            mpn = entry.get('mpn')
            if mpn is None:
                problematic_entries.append(f"Entry {idx}: MPN is None")
                continue
                
            products = entry.get('products', [])
            if not products:
                problematic_entries.append(f"Entry {idx}: No products for MPN {mpn}")
                continue
                
            for prod_idx, product in enumerate(products):
                if product is None:
                    problematic_entries.append(f"Entry {idx}, Product {prod_idx}: Product is None")
                    continue
                    
                name = product.get('name')
                if name is None:
                    problematic_entries.append(f"Entry {idx}, Product {prod_idx}: Name is None for MPN {mpn}")
        
        logger.info(f"Found {len(problematic_entries)} problematic entries")
        for entry in problematic_entries[:20]:  # Show first 20
            logger.warning(entry)
            
        return problematic_entries
        
    except Exception as e:
        logger.error(f"Error inspecting entries: {e}")
        return []

if __name__ == "__main__":
    # First inspect problematic entries
    problematic = inspect_problematic_entries()
    
    if len(problematic) < 100:  # If not too many issues, proceed with test
        logger.info("Proceeding with small scale test...")
        success = test_small_dataset()
        
        if success:
            logger.info("✅ Test completed successfully! Ready for full scale processing.")
        else:
            logger.error("❌ Test failed. Need to fix issues before full processing.")
    else:
        logger.error(f"Too many problematic entries ({len(problematic)}). Need to fix data issues first.")
