#!/usr/bin/env python3
"""
Generate Fast Autocomplete Data
==============================

Creates optimized autocomplete data from ChromaDB for instant search suggestions.
"""

import json
import logging
import chromadb
from collections import defaultdict
import re
from typing import Dict, List, Set

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AutocompleteDataGenerator:
    """Generate optimized autocomplete data from ChromaDB."""
    
    def __init__(self, db_path: str = "./chroma_db", collection_name: str = "product_embeddings"):
        """Initialize the generator."""
        self.db_path = db_path
        self.collection_name = collection_name
        self.client = None
        self.collection = None
        
    def connect_to_database(self):
        """Connect to ChromaDB."""
        logger.info(f"Connecting to ChromaDB at {self.db_path}")
        self.client = chromadb.PersistentClient(path=self.db_path)
        self.collection = self.client.get_collection(self.collection_name)
        logger.info(f"Connected to collection with {self.collection.count():,} products")
    
    def extract_products_data(self) -> List[Dict]:
        """Extract essential product data for autocomplete."""
        logger.info("Extracting product data for autocomplete...")
        
        # Get all products in batches
        batch_size = 5000
        all_products = []
        
        total_count = self.collection.count()
        processed = 0
        
        while processed < total_count:
            logger.info(f"Processing batch {processed//batch_size + 1}, items {processed}-{min(processed + batch_size, total_count)}")
            
            # Get batch of products
            results = self.collection.get(
                limit=batch_size,
                offset=processed,
                include=['documents', 'metadatas']
            )
            
            # Process each product
            for i, (doc, metadata) in enumerate(zip(results['documents'], results['metadatas'])):
                try:
                    # Parse the document format: "name | brand | maincat | subcat"
                    parts = doc.split(' | ')
                    if len(parts) >= 3:
                        name = parts[0].strip()
                        brand = parts[1].strip()
                        category = parts[2].strip()
                        subcategory = parts[3].strip() if len(parts) > 3 else ""
                        
                        # Get MPN from metadata
                        mpn = metadata.get('mpn', '').strip()
                        seller = metadata.get('seller', '').strip()
                        url = metadata.get('url', '').strip()
                        
                        if name and brand and mpn:  # Only include products with essential data
                            product = {
                                'id': f"{processed + i}",
                                'name': name,
                                'brand': brand,
                                'mpn': mpn,
                                'category': category,
                                'subcategory': subcategory,
                                'seller': seller,
                                'url': url
                            }
                            all_products.append(product)
                
                except Exception as e:
                    logger.warning(f"Error processing product {processed + i}: {e}")
                    continue
            
            processed += batch_size
        
        logger.info(f"Extracted {len(all_products):,} valid products for autocomplete")
        return all_products
    
    def create_search_indices(self, products: List[Dict]) -> Dict:
        """Create optimized search indices for fast autocomplete."""
        logger.info("Creating search indices...")
        
        # Create different types of search indices
        indices = {
            'by_name_prefix': defaultdict(list),      # name starts with
            'by_name_contains': defaultdict(list),    # name contains
            'by_mpn_prefix': defaultdict(list),       # MPN starts with
            'by_mpn_exact': defaultdict(list),        # exact MPN match
            'by_brand_prefix': defaultdict(list),     # brand starts with
            'popular_products': [],                   # most common products
            'all_products': products                  # full product list
        }
        
        # Build indices
        for product in products:
            name = product['name'].lower()
            mpn = product['mpn'].lower()
            brand = product['brand'].lower()
            
            # Name-based indices
            # Add prefixes (1-4 characters)
            for i in range(1, min(len(name) + 1, 5)):
                prefix = name[:i]
                if len(prefix) >= 2:  # Only index 2+ character prefixes
                    indices['by_name_prefix'][prefix].append(product)
            
            # Add word-based contains matching
            words = re.findall(r'\b\w+\b', name)
            for word in words:
                if len(word) >= 2:
                    indices['by_name_contains'][word].append(product)
            
            # MPN-based indices
            if mpn:
                # MPN prefixes
                for i in range(1, min(len(mpn) + 1, 8)):
                    prefix = mpn[:i]
                    if len(prefix) >= 2:
                        indices['by_mpn_prefix'][prefix].append(product)
                
                # Exact MPN
                indices['by_mpn_exact'][mpn].append(product)
            
            # Brand prefixes
            if brand:
                for i in range(1, min(len(brand) + 1, 5)):
                    prefix = brand[:i]
                    if len(prefix) >= 2:
                        indices['by_brand_prefix'][prefix].append(product)
        
        # Create popular products list (products that appear in multiple categories)
        product_frequency = defaultdict(int)
        for product in products:
            key = f"{product['name']}|{product['brand']}"
            product_frequency[key] += 1
        
        # Sort by frequency and take top products
        popular_items = sorted(product_frequency.items(), key=lambda x: x[1], reverse=True)[:1000]
        popular_product_keys = {item[0] for item in popular_items}
        
        indices['popular_products'] = [
            product for product in products 
            if f"{product['name']}|{product['brand']}" in popular_product_keys
        ][:500]  # Limit to top 500
        
        # Convert defaultdicts to regular dicts and limit sizes
        for key in ['by_name_prefix', 'by_name_contains', 'by_mpn_prefix', 'by_brand_prefix']:
            indices[key] = {
                k: v[:20] for k, v in indices[key].items()  # Limit each index to 20 items
            }
        
        indices['by_mpn_exact'] = {
            k: v[:10] for k, v in indices['by_mpn_exact'].items()  # Limit MPN matches to 10
        }
        
        logger.info(f"Created indices with:")
        logger.info(f"  - Name prefixes: {len(indices['by_name_prefix']):,}")
        logger.info(f"  - Name contains: {len(indices['by_name_contains']):,}")
        logger.info(f"  - MPN prefixes: {len(indices['by_mpn_prefix']):,}")
        logger.info(f"  - MPN exact: {len(indices['by_mpn_exact']):,}")
        logger.info(f"  - Brand prefixes: {len(indices['by_brand_prefix']):,}")
        logger.info(f"  - Popular products: {len(indices['popular_products']):,}")
        
        return indices
    
    def save_autocomplete_data(self, indices: Dict, output_file: str = "static/data/autocomplete.json"):
        """Save autocomplete data to JSON file."""
        import os
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        logger.info(f"Saving autocomplete data to {output_file}")
        
        # Calculate file size estimate
        import sys
        size_estimate = sys.getsizeof(str(indices)) / (1024 * 1024)  # MB
        logger.info(f"Estimated file size: {size_estimate:.2f} MB")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(indices, f, separators=(',', ':'))  # Compact JSON
        
        # Get actual file size
        actual_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
        logger.info(f"Actual file size: {actual_size:.2f} MB")
        logger.info(f"✅ Autocomplete data saved successfully!")
    
    def generate_autocomplete_data(self, output_file: str = "static/data/autocomplete.json"):
        """Main method to generate autocomplete data."""
        logger.info("🚀 Starting autocomplete data generation...")
        
        # Connect to database
        self.connect_to_database()
        
        # Extract product data
        products = self.extract_products_data()
        
        if not products:
            logger.error("❌ No products found!")
            return False
        
        # Create search indices
        indices = self.create_search_indices(products)
        
        # Save to file
        self.save_autocomplete_data(indices, output_file)
        
        logger.info("🎉 Autocomplete data generation completed!")
        return True


def main():
    """Main function."""
    generator = AutocompleteDataGenerator()
    success = generator.generate_autocomplete_data()
    
    if success:
        logger.info("✅ Ready to use fast autocomplete!")
        logger.info("📁 File location: static/data/autocomplete.json")
        logger.info("🔧 Next: Update the frontend to use this data")
    else:
        logger.error("❌ Failed to generate autocomplete data")


if __name__ == "__main__":
    main()
