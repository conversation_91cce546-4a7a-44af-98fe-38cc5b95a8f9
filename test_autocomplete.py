#!/usr/bin/env python3
"""
Test Fast Autocomplete Service
=============================

Test the autocomplete service directly to verify functionality.
"""

import asyncio
import time
import logging
from app.services.autocomplete_service import FastAutocompleteService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_autocomplete():
    """Test the autocomplete service."""
    logger.info("🧪 Testing Fast Autocomplete Service")
    
    # Initialize service
    service = FastAutocompleteService()
    await service.initialize()
    
    # Test queries
    test_queries = [
        "dental",
        "acrylic",
        "6010002",  # Known MPN
        "millennium",
        "keystone",
        "resin"
    ]
    
    for query in test_queries:
        logger.info(f"\n🔍 Testing query: '{query}'")
        start_time = time.time()
        
        try:
            suggestions = await service.get_suggestions(query, max_results=5)
            response_time = (time.time() - start_time) * 1000
            
            logger.info(f"⏱️  Response time: {response_time:.2f}ms")
            logger.info(f"📊 Found {len(suggestions)} suggestions:")
            
            for i, suggestion in enumerate(suggestions, 1):
                logger.info(f"  {i}. {suggestion['text']}")
                logger.info(f"     Brand: {suggestion['brand']}, MPN: {suggestion['mpn']}")
                logger.info(f"     Match: {suggestion['match_type']}")
                
        except Exception as e:
            logger.error(f"❌ Error testing '{query}': {e}")
    
    await service.close()
    logger.info("\n✅ Autocomplete testing completed!")


if __name__ == "__main__":
    asyncio.run(test_autocomplete())
