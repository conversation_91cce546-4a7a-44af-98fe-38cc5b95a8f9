#!/usr/bin/env python3
"""
Test Model Compatibility
========================

This script tests if the embedding model matches the database dimensions.
"""

import logging
import chromadb
from sentence_transformers import SentenceTransformer

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_model_compatibility():
    """Test if the model dimensions match the database."""
    
    try:
        # Load the same model used for indexing
        model_name = "BAAI/bge-large-en-v1.5"
        logger.info(f"Loading model: {model_name}")
        model = SentenceTransformer(model_name)
        
        # Test embedding generation
        test_query = "dental acrylic resin"
        embedding = model.encode([test_query])
        logger.info(f"Generated embedding shape: {embedding.shape}")
        logger.info(f"Embedding dimensions: {embedding.shape[1]}")
        
        # Connect to database
        logger.info("Connecting to ChromaDB...")
        client = chromadb.PersistentClient(path="./chroma_db")
        collection = client.get_collection(name="product_embeddings")
        
        logger.info(f"Collection count: {collection.count()}")
        logger.info(f"Collection metadata: {collection.metadata}")
        
        # Test a simple query
        logger.info("Testing query with embeddings...")
        results = collection.query(
            query_embeddings=embedding.tolist(),
            n_results=3,
            include=['documents', 'metadatas', 'distances']
        )
        
        logger.info(f"Query successful! Found {len(results['documents'][0])} results")
        
        # Show first result
        if results['documents'][0]:
            first_result = results['documents'][0][0]
            first_metadata = results['metadatas'][0][0]
            first_distance = results['distances'][0][0]
            
            logger.info(f"First result:")
            logger.info(f"  Document: {first_result}")
            logger.info(f"  MPN: {first_metadata.get('mpn', 'N/A')}")
            logger.info(f"  Brand: {first_metadata.get('brand', 'N/A')}")
            logger.info(f"  Distance: {first_distance:.4f}")
        
        logger.info("✅ Model compatibility test PASSED!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Model compatibility test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_model_compatibility()
