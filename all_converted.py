import json
import os

def transform_dental_data(input_filename="all.json", output_filename="products.json"):
    """
    Reads all.json, processes the product data, and writes it to products.json
    in the specified format (mpn as key, list of concatenated product strings as value).
    """
    
    # Check if the input file exists
    if not os.path.exists(input_filename):
        print(f"Error: Input file '{input_filename}' not found. Please ensure it's in the same directory.")
        return

    transformed_data = {} # This will store the final structured data

    try:
        with open(input_filename, 'r', encoding='utf-8') as f:
            source_data = json.load(f)
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from '{input_filename}'. Check its format.")
        return
    except Exception as e:
        print(f"An unexpected error occurred while reading '{input_filename}': {e}")
        return

    # Iterate through each top-level entry (which has an 'mpn' and 'products' list)
    for entry in source_data:
        mpn = entry.get("mpn")
        products_list = entry.get("products", [])

        if mpn: # Proceed only if an MPN is found for the entry
            # Ensure the MPN key exists in our transformed_data dictionary
            if mpn not in transformed_data:
                transformed_data[mpn] = []
            
            # Iterate through each individual product within the 'products' list
            for product in products_list:
                # Safely get values, defaulting to empty string if None or not found
                brand = product.get("brand", "") or ""
                name = product.get("name", "") or ""
                maincat = product.get("maincat", "") or ""
                subcat = product.get("subcat", "") or ""
                seller = product.get("seller", "") or ""
                mfg = product.get("mpn", "") or ""

                # Concatenate the fields with " | " as separator
                concatenated_string = f"Brand:{brand.strip()} |MFG: {mfg.strip()} | name: {name.strip()} | Category: {maincat.strip()} | subcategory: {subcat.strip()} | seller: {seller.strip()}"
                
                # Add the concatenated string to the list for the current MPN
                transformed_data[mpn].append(concatenated_string)
        else:
            print(f"Warning: Skipping an entry due to missing 'mpn' field: {entry}")

    # Write the transformed data to the output JSON file
    try:
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(transformed_data, f, indent=4, ensure_ascii=False)
        print(f"Successfully processed '{input_filename}' and saved to '{output_filename}'.")
    except Exception as e:
        print(f"An error occurred while writing to '{output_filename}': {e}")

# --- Execute the transformation ---
if __name__ == "__main__":
    transform_dental_data()