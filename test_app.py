#!/usr/bin/env python3
"""
Application Testing Script
=========================

Comprehensive testing script for the Product Search & Price Comparison application.
"""

import asyncio
import logging
import sys
import json
from pathlib import Path
import httpx
import time

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent))

from app.config import get_settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

settings = get_settings()


class AppTester:
    """Test suite for the application."""
    
    def __init__(self, base_url: str = None):
        self.base_url = base_url or f"http://{settings.host}:{settings.port}"
        self.session_token = None
        self.test_results = []
    
    async def run_all_tests(self):
        """Run all test suites."""
        logger.info("🧪 Starting comprehensive application tests")
        logger.info(f"Testing against: {self.base_url}")
        
        # Test suites
        test_suites = [
            ("Health Check", self.test_health_check),
            ("Session Management", self.test_session_management),
            ("Search Functionality", self.test_search_functionality),
            ("Autocomplete", self.test_autocomplete),
            ("MPN Search", self.test_mpn_search),
            ("Wishlist Management", self.test_wishlist_management),
            ("Price Comparison", self.test_price_comparison),
        ]
        
        for suite_name, test_func in test_suites:
            logger.info(f"\n📋 Running {suite_name} tests...")
            try:
                await test_func()
                self.test_results.append((suite_name, "PASSED", None))
                logger.info(f"✅ {suite_name} tests PASSED")
            except Exception as e:
                self.test_results.append((suite_name, "FAILED", str(e)))
                logger.error(f"❌ {suite_name} tests FAILED: {e}")
        
        self.print_test_summary()
    
    async def test_health_check(self):
        """Test health check endpoint."""
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.base_url}/health")
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
            logger.info("Health check endpoint working")
    
    async def test_session_management(self):
        """Test session creation and management."""
        async with httpx.AsyncClient() as client:
            # Create session
            response = await client.post(f"{self.base_url}/api/session/create")
            assert response.status_code == 200
            data = response.json()
            assert "session_token" in data
            
            self.session_token = data["session_token"]
            logger.info(f"Session created: {self.session_token[:8]}...")
            
            # Get session info
            response = await client.get(f"{self.base_url}/api/session/info/{self.session_token}")
            assert response.status_code == 200
            session_data = response.json()
            assert session_data["session_token"] == self.session_token
            logger.info("Session info retrieval working")
    
    async def test_search_functionality(self):
        """Test semantic search functionality."""
        if not self.session_token:
            await self.test_session_management()
        
        async with httpx.AsyncClient() as client:
            # Test search
            search_data = {
                "query": "dental acrylic resin",
                "max_results": 10,
                "similarity_threshold": 0.3
            }
            
            response = await client.post(
                f"{self.base_url}/api/search/",
                json=search_data,
                headers={"X-Session-Token": self.session_token}
            )
            assert response.status_code == 200
            
            results = response.json()
            assert "query" in results
            assert "exact_matches" in results
            assert "high_confidence_similar" in results
            assert "total_results" in results
            assert "performance" in results
            
            total_results = (
                len(results["exact_matches"]) +
                len(results["high_confidence_similar"]) +
                len(results["medium_confidence_similar"]) +
                len(results["category_related"])
            )
            
            logger.info(f"Search returned {total_results} results")
            logger.info(f"Search took {results['performance']['search_duration_ms']:.2f}ms")
            
            # Store first result for wishlist testing
            if results["exact_matches"]:
                self.test_product = results["exact_matches"][0]
            elif results["high_confidence_similar"]:
                self.test_product = results["high_confidence_similar"][0]
            else:
                raise Exception("No search results found for testing")
    
    async def test_autocomplete(self):
        """Test autocomplete functionality."""
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.base_url}/api/search/suggestions/dental?limit=5")
            assert response.status_code == 200
            
            data = response.json()
            assert "suggestions" in data
            logger.info(f"Autocomplete returned {len(data['suggestions'])} suggestions")
    
    async def test_mpn_search(self):
        """Test MPN search functionality."""
        async with httpx.AsyncClient() as client:
            # Test with a known MPN
            test_mpn = "6010002"
            response = await client.get(
                f"{self.base_url}/api/search/mpn/{test_mpn}",
                headers={"X-Session-Token": self.session_token}
            )
            
            if response.status_code == 200:
                results = response.json()
                assert "mpn" in results
                assert results["mpn"] == test_mpn
                logger.info(f"MPN search found {results['total_variants']} variants")
            else:
                logger.warning(f"MPN {test_mpn} not found in database")
    
    async def test_wishlist_management(self):
        """Test wishlist functionality."""
        if not hasattr(self, 'test_product'):
            await self.test_search_functionality()
        
        async with httpx.AsyncClient() as client:
            # Add to wishlist
            wishlist_data = {
                "product_id": self.test_product["product_id"],
                "mpn": self.test_product["mpn"],
                "product_name": self.test_product["name"],
                "brand": self.test_product["brand"],
                "category": self.test_product["category"],
                "subcategory": self.test_product.get("subcategory"),
                "seller": self.test_product.get("seller"),
                "price": self.test_product.get("price"),
                "product_url": self.test_product.get("product_url"),
                "similarity_score": self.test_product["similarity_score"]
            }
            
            response = await client.post(
                f"{self.base_url}/api/wishlist/add",
                json=wishlist_data,
                headers={"X-Session-Token": self.session_token}
            )
            assert response.status_code == 200
            
            add_result = response.json()
            assert add_result["success"] == True
            self.wishlist_item_id = add_result["item_id"]
            logger.info("Product added to wishlist successfully")
            
            # Get wishlist
            response = await client.get(
                f"{self.base_url}/api/wishlist/",
                headers={"X-Session-Token": self.session_token}
            )
            assert response.status_code == 200
            
            wishlist = response.json()
            assert wishlist["total_items"] >= 1
            logger.info(f"Wishlist contains {wishlist['total_items']} items")
            
            # Get wishlist summary
            response = await client.get(
                f"{self.base_url}/api/wishlist/summary",
                headers={"X-Session-Token": self.session_token}
            )
            assert response.status_code == 200
            
            summary = response.json()
            assert summary["total_items"] >= 1
            logger.info("Wishlist summary retrieved successfully")
    
    async def test_price_comparison(self):
        """Test price comparison functionality."""
        if not hasattr(self, 'wishlist_item_id'):
            await self.test_wishlist_management()
        
        async with httpx.AsyncClient(timeout=60.0) as client:  # Longer timeout for price comparison
            response = await client.post(
                f"{self.base_url}/api/wishlist/compare-prices",
                headers={"X-Session-Token": self.session_token}
            )
            assert response.status_code == 200
            
            comparison = response.json()
            assert "comparison_results" in comparison
            assert "summary" in comparison
            
            logger.info(f"Price comparison completed for {comparison['total_items']} items")
            logger.info(f"Found alternatives for {comparison['items_with_alternatives']} items")
            
            if comparison["potential_total_savings"] > 0:
                logger.info(f"Potential savings: ${comparison['potential_total_savings']:.2f}")
    
    def print_test_summary(self):
        """Print test results summary."""
        logger.info("\n" + "="*60)
        logger.info("🧪 TEST RESULTS SUMMARY")
        logger.info("="*60)
        
        passed = 0
        failed = 0
        
        for suite_name, status, error in self.test_results:
            status_icon = "✅" if status == "PASSED" else "❌"
            logger.info(f"{status_icon} {suite_name}: {status}")
            
            if status == "PASSED":
                passed += 1
            else:
                failed += 1
                if error:
                    logger.info(f"   Error: {error}")
        
        logger.info("-"*60)
        logger.info(f"Total: {len(self.test_results)} | Passed: {passed} | Failed: {failed}")
        
        if failed == 0:
            logger.info("🎉 All tests PASSED!")
        else:
            logger.info(f"⚠️  {failed} test suite(s) FAILED")
        
        logger.info("="*60)


async def main():
    """Main testing function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test the Product Search application")
    parser.add_argument("--url", default=None, help="Base URL to test against")
    parser.add_argument("--wait", type=int, default=5, help="Seconds to wait before starting tests")
    
    args = parser.parse_args()
    
    if args.wait > 0:
        logger.info(f"⏳ Waiting {args.wait} seconds for application to start...")
        await asyncio.sleep(args.wait)
    
    tester = AppTester(base_url=args.url)
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
