"""
Application Configuration
========================

Configuration settings for the product search and price comparison application.
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application
    app_name: str = "Product Search & Price Comparison"
    app_version: str = "1.0.0"
    debug: bool = Field(default=False, env="DEBUG")
    
    # Server
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    
    # Database - PostgreSQL
    postgres_host: str = Field(default="localhost", env="POSTGRES_HOST")
    postgres_port: int = Field(default=5432, env="POSTGRES_PORT")
    postgres_user: str = Field(default="postgres", env="POSTGRES_USER")
    postgres_password: str = Field(default="password", env="POSTGRES_PASSWORD")
    postgres_db: str = Field(default="product_search", env="POSTGRES_DB")
    
    @property
    def postgres_url(self) -> str:
        """Get database connection URL."""
        # Use SQLite for local development if PostgreSQL is not available
        if self.debug:
            return "sqlite+aiosqlite:///./product_search.db"
        return f"postgresql+asyncpg://{self.postgres_user}:{self.postgres_password}@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"

    @property
    def postgres_sync_url(self) -> str:
        """Get synchronous database connection URL for Alembic."""
        # Use SQLite for local development if PostgreSQL is not available
        if self.debug:
            return "sqlite:///./product_search.db"
        return f"postgresql://{self.postgres_user}:{self.postgres_password}@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"
    
    # ChromaDB
    chroma_db_path: str = Field(default="./chroma_db", env="CHROMA_DB_PATH")
    chroma_collection_name: str = Field(default="product_embeddings", env="CHROMA_COLLECTION_NAME")
    
    # Embedding Model
    embedding_model_name: str = Field(default="BAAI/bge-large-en-v1.5", env="EMBEDDING_MODEL_NAME")
    
    # Search Configuration
    search_max_results: int = Field(default=20, env="SEARCH_MAX_RESULTS")
    similarity_threshold: float = Field(default=0.3, env="SIMILARITY_THRESHOLD")
    autocomplete_max_results: int = Field(default=10, env="AUTOCOMPLETE_MAX_RESULTS")
    
    # Session & Security
    secret_key: str = Field(default="your-secret-key-change-in-production", env="SECRET_KEY")
    session_expire_hours: int = Field(default=24, env="SESSION_EXPIRE_HOURS")
    
    # Redis (for caching and sessions)
    redis_host: str = Field(default="localhost", env="REDIS_HOST")
    redis_port: int = Field(default=6379, env="REDIS_PORT")
    redis_db: int = Field(default=0, env="REDIS_DB")
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    
    @property
    def redis_url(self) -> str:
        """Get Redis connection URL."""
        if self.redis_password:
            return f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}/{self.redis_db}"
        return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"
    
    # CORS
    cors_origins: str = Field(
        default="http://localhost:3000,http://localhost:8000,http://127.0.0.1:8000",
        env="CORS_ORIGINS"
    )

    @property
    def cors_origins_list(self) -> list[str]:
        """Get CORS origins as a list."""
        return [origin.strip() for origin in self.cors_origins.split(",")]
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Performance
    max_concurrent_searches: int = Field(default=10, env="MAX_CONCURRENT_SEARCHES")
    search_timeout_seconds: int = Field(default=30, env="SEARCH_TIMEOUT_SECONDS")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings
