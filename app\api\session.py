"""
Session API Endpoints
====================

FastAPI endpoints for session management.
"""

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import logging
from datetime import datetime, timedelta
import uuid

from app.database import get_db
from app.models.user_session import UserSession

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/create")
async def create_session(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """Create a new user session."""
    try:
        # Create new session
        session = UserSession(
            session_token=str(uuid.uuid4()),
            expires_at=datetime.utcnow() + timedelta(hours=24),
            is_active=True,
            user_agent=request.headers.get("User-Agent"),
            ip_address=request.client.host if request.client else None
        )
        
        db.add(session)
        await db.commit()
        await db.refresh(session)
        
        return {
            "session_token": session.session_token,
            "expires_at": session.expires_at.isoformat(),
            "created_at": session.created_at.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error creating session: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create session")


@router.get("/info/{session_token}")
async def get_session_info(
    session_token: str,
    db: AsyncSession = Depends(get_db)
):
    """Get session information."""
    try:
        result = await db.execute(
            select(UserSession).where(
                UserSession.session_token == session_token,
                UserSession.is_active == True
            )
        )
        session = result.scalar_one_or_none()
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return session.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session info: {e}")
        raise HTTPException(status_code=500, detail="Failed to get session info")
