# Deployment Guide

This guide covers deploying the Product Search & Price Comparison application to production environments.

## 🚀 Production Deployment

### Docker Deployment (Recommended)

#### 1. Create Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Start application
CMD ["python", "run_app.py"]
```

#### 2. Create docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - POSTGRES_HOST=db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=your_secure_password
      - POSTGRES_DB=product_search
      - SECRET_KEY=your_very_secure_secret_key
      - DEBUG=false
    volumes:
      - ./chroma_db:/app/chroma_db:ro
    depends_on:
      - db
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=your_secure_password
      - POSTGRES_DB=product_search
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
```

#### 3. Create nginx.conf
```nginx
events {
    worker_connections 1024;
}

http {
    upstream app {
        server app:8000;
    }

    server {
        listen 80;
        server_name your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        client_max_body_size 10M;

        location / {
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /static/ {
            proxy_pass http://app;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

#### 4. Deploy
```bash
# Build and start services
docker-compose up -d

# Check logs
docker-compose logs -f app

# Run database setup (first time only)
docker-compose exec app python setup_database.py
```

### Cloud Deployment Options

#### AWS Deployment
1. **ECS with Fargate**: Use the Docker setup with AWS ECS
2. **RDS PostgreSQL**: Use managed PostgreSQL service
3. **EFS**: Mount ChromaDB data on EFS for persistence
4. **ALB**: Use Application Load Balancer for SSL termination

#### Google Cloud Platform
1. **Cloud Run**: Deploy containerized application
2. **Cloud SQL**: Use managed PostgreSQL
3. **Cloud Storage**: Store ChromaDB data
4. **Cloud Load Balancing**: Handle SSL and traffic distribution

#### Azure Deployment
1. **Container Instances**: Deploy Docker containers
2. **Azure Database for PostgreSQL**: Managed database
3. **Azure Files**: Store ChromaDB data
4. **Application Gateway**: SSL termination and load balancing

## 🔧 Production Configuration

### Environment Variables
```bash
# Security
SECRET_KEY=your-very-secure-secret-key-here
DEBUG=false

# Database
POSTGRES_HOST=your-db-host
POSTGRES_PORT=5432
POSTGRES_USER=your-db-user
POSTGRES_PASSWORD=your-secure-password
POSTGRES_DB=product_search

# Performance
MAX_CONCURRENT_SEARCHES=20
SEARCH_TIMEOUT_SECONDS=30

# Monitoring
LOG_LEVEL=INFO
```

### Performance Optimization

#### 1. Database Optimization
```sql
-- Create indexes for better performance
CREATE INDEX CONCURRENTLY idx_search_history_session_time 
ON search_history(session_id, searched_at);

CREATE INDEX CONCURRENTLY idx_wishlist_session_mpn 
ON wishlist_items(session_id, mpn);

CREATE INDEX CONCURRENTLY idx_product_analytics_popularity 
ON product_analytics(popularity_score DESC);
```

#### 2. Application Optimization
- Use connection pooling for database
- Implement Redis for session caching
- Enable gzip compression
- Use CDN for static assets

#### 3. ChromaDB Optimization
- Use SSD storage for ChromaDB
- Allocate sufficient RAM for embedding model
- Consider model quantization for memory efficiency

## 📊 Monitoring & Logging

### Health Checks
```bash
# Application health
curl http://localhost:8000/health

# Database connectivity
curl http://localhost:8000/api/info

# Search functionality
curl -X POST http://localhost:8000/api/search/ \
  -H "Content-Type: application/json" \
  -d '{"query": "test", "max_results": 1}'
```

### Logging Configuration
```python
# In production, use structured logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'json': {
            'format': '%(asctime)s %(name)s %(levelname)s %(message)s',
            'class': 'pythonjsonlogger.jsonlogger.JsonFormatter'
        }
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/app/app.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'formatter': 'json'
        }
    },
    'root': {
        'level': 'INFO',
        'handlers': ['file']
    }
}
```

### Metrics Collection
- Use Prometheus for metrics collection
- Monitor search response times
- Track wishlist operations
- Monitor database performance
- Alert on error rates

## 🔒 Security Considerations

### Application Security
- Use HTTPS in production
- Implement rate limiting
- Validate all input data
- Use secure session tokens
- Regular security updates

### Database Security
- Use strong passwords
- Enable SSL connections
- Restrict network access
- Regular backups
- Monitor for suspicious activity

### Infrastructure Security
- Use VPC/private networks
- Implement firewall rules
- Regular security patches
- Access logging
- Principle of least privilege

## 🔄 Backup & Recovery

### Database Backup
```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h $POSTGRES_HOST -U $POSTGRES_USER $POSTGRES_DB > backup_$DATE.sql
aws s3 cp backup_$DATE.sql s3://your-backup-bucket/
```

### ChromaDB Backup
```bash
# Backup ChromaDB data
tar -czf chroma_backup_$(date +%Y%m%d).tar.gz chroma_db/
aws s3 cp chroma_backup_$(date +%Y%m%d).tar.gz s3://your-backup-bucket/
```

### Recovery Procedures
1. **Database Recovery**: Restore from PostgreSQL backup
2. **ChromaDB Recovery**: Extract backup and restart application
3. **Full System Recovery**: Use infrastructure as code to rebuild

## 📈 Scaling Considerations

### Horizontal Scaling
- Use load balancer for multiple app instances
- Implement session affinity or shared session storage
- Use read replicas for database scaling

### Vertical Scaling
- Increase memory for embedding model
- Use faster storage for ChromaDB
- Optimize database configuration

### Caching Strategy
- Implement Redis for search result caching
- Cache popular product data
- Use CDN for static assets

---

**Note**: This deployment guide assumes familiarity with Docker, cloud platforms, and production deployment practices. Adjust configurations based on your specific requirements and infrastructure.
