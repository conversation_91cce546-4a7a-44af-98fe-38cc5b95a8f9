// Product Search & Price Comparison - Main JavaScript

class ProductSearchApp {
    constructor() {
        this.sessionToken = this.getOrCreateSessionToken();
        this.searchTimeout = null;
        this.wishlist = [];
        this.currentSearchResults = null;
        
        this.initializeEventListeners();
        this.loadWishlist();
    }
    
    // Session Management
    getOrCreateSessionToken() {
        let token = localStorage.getItem('session_token');
        if (!token) {
            this.createNewSession();
        }
        return token;
    }
    
    async createNewSession() {
        try {
            const response = await fetch('/api/session/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                localStorage.setItem('session_token', data.session_token);
                this.sessionToken = data.session_token;
            }
        } catch (error) {
            console.error('Failed to create session:', error);
        }
    }
    
    // Event Listeners
    initializeEventListeners() {
        const searchInput = document.getElementById('search-input');
        const comparePricesBtn = document.getElementById('compare-prices-btn');
        const clearWishlistBtn = document.getElementById('clear-wishlist-btn');
        
        // Search input with debouncing
        searchInput.addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            const query = e.target.value.trim();
            this.currentQuery = query;

            if (query.length >= 2) {
                // Show autocomplete suggestions immediately (fast)
                this.showAutocompleteSuggestions(query);

                // Debounce full search (slower)
                this.searchTimeout = setTimeout(() => {
                    this.performSearch(query);
                }, 500); // Increased debounce for full search
            } else {
                this.hideSearchResults();
                this.hideAutocompleteSuggestions();
            }
        });
        
        // Search input focus/blur for suggestions
        searchInput.addEventListener('focus', () => {
            const query = searchInput.value.trim();
            if (query.length >= 2) {
                this.showAutocompleteSuggestions(query);
            }
        });
        
        searchInput.addEventListener('blur', () => {
            // Delay hiding to allow clicking on suggestions
            setTimeout(() => {
                this.hideAutocompleteSuggestions();
            }, 200);
        });
        
        // Wishlist actions
        comparePricesBtn.addEventListener('click', () => {
            this.comparePrices();
        });
        
        clearWishlistBtn.addEventListener('click', () => {
            this.clearWishlist();
        });
    }
    
    // Search Functionality
    async performSearch(query) {
        this.showLoading('Searching products...');

        try {
            const response = await fetch('/api/search/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Session-Token': this.sessionToken
                },
                body: JSON.stringify({
                    query: query,
                    max_results: 20,
                    similarity_threshold: 0.3
                })
            });

            if (response.ok) {
                const results = await response.json();
                this.currentSearchResults = results;
                this.displaySearchResults(results);
            } else {
                // Fallback to quick search if full search fails
                console.warn('Full search failed, trying quick search...');
                await this.performQuickSearch(query);
            }
        } catch (error) {
            console.error('Search error:', error);
            // Fallback to quick search
            console.warn('Full search error, trying quick search...');
            await this.performQuickSearch(query);
        } finally {
            this.hideLoading();
        }
    }

    async performQuickSearch(query) {
        this.showLoading('Quick search...');

        try {
            const response = await fetch(`/api/autocomplete/search/${encodeURIComponent(query)}?limit=10`);

            if (response.ok) {
                const data = await response.json();
                if (data.status === 'success') {
                    // Convert quick search results to display format
                    const results = {
                        query: query,
                        exact_matches: data.results.filter(r => r.match_type === 'mpn_exact'),
                        high_confidence_similar: data.results.filter(r => r.match_type === 'name_prefix'),
                        medium_confidence_similar: data.results.filter(r => r.match_type === 'name_contains'),
                        category_related: data.results.filter(r => r.match_type === 'brand'),
                        total_results: data.total_results,
                        performance: {
                            search_duration_ms: data.response_time_ms,
                            search_type: 'quick_search'
                        }
                    };

                    this.currentSearchResults = results;
                    this.displaySearchResults(results);
                } else {
                    this.showError('Quick search failed. Please try again.');
                }
            } else {
                this.showError('Search service unavailable. Please try again later.');
            }
        } catch (error) {
            console.error('Quick search error:', error);
            this.showError('Search failed. Please check your connection.');
        } finally {
            this.hideLoading();
        }
    }
    
    async showAutocompleteSuggestions(query) {
        try {
            // Use the new fast autocomplete endpoint
            const response = await fetch(`/api/autocomplete/suggestions?q=${encodeURIComponent(query)}&limit=8`);

            if (response.ok) {
                const data = await response.json();
                if (data.status === 'success') {
                    this.displayAutocompleteSuggestions(data.suggestions);

                    // Log performance for debugging
                    if (data.response_time_ms > 100) {
                        console.warn(`Slow autocomplete: ${data.response_time_ms}ms for "${query}"`);
                    }
                } else {
                    console.error('Autocomplete service error:', data.error);
                }
            }
        } catch (error) {
            console.error('Autocomplete error:', error);
        }
    }
    
    displayAutocompleteSuggestions(suggestions) {
        const suggestionsContainer = document.getElementById('search-suggestions');

        if (suggestions.length === 0) {
            this.hideAutocompleteSuggestions();
            return;
        }

        suggestionsContainer.innerHTML = suggestions.map(suggestion => {
            // Add match type indicators
            const matchIcon = this.getMatchTypeIcon(suggestion.match_type);
            const matchClass = `suggestion-${suggestion.match_type}`;

            return `
                <div class="suggestion-item ${matchClass}">
                    <div class="suggestion-content" onclick="app.selectSuggestion('${suggestion.text.replace(/'/g, "\\'")}', '${suggestion.product_id}')">
                        <div class="suggestion-text">
                            ${matchIcon} ${this.highlightMatch(suggestion.text, this.currentQuery)}
                        </div>
                        <div class="suggestion-meta">
                            🏷️ ${suggestion.brand} • 🔢 ${suggestion.mpn} • 📂 ${suggestion.category}
                        </div>
                    </div>
                    <div class="suggestion-actions">
                        <button class="btn btn-primary btn-xs" onclick="app.addToWishlistFromSuggestion('${suggestion.product_id}', '${suggestion.text.replace(/'/g, "\\'")}', '${suggestion.brand.replace(/'/g, "\\'")}', '${suggestion.mpn.replace(/'/g, "\\'")}', '${suggestion.category.replace(/'/g, "\\'")}', '${(suggestion.url || '').replace(/'/g, "\\'")}'); event.stopPropagation();">
                            + Add
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        suggestionsContainer.classList.add('show');
    }

    getMatchTypeIcon(matchType) {
        const icons = {
            'mpn_exact': '🎯',      // Exact MPN match
            'name_prefix': '📝',    // Name starts with query
            'name_contains': '🔍',  // Name contains query
            'brand': '🏷️',         // Brand match
            'fuzzy': '💡',          // Fuzzy match
            'popular': '⭐'         // Popular product
        };
        return icons[matchType] || '📦';
    }

    highlightMatch(text, query) {
        if (!query || query.length < 2) return text;

        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<strong>$1</strong>');
    }
    
    selectSuggestion(text, productId = null) {
        const searchInput = document.getElementById('search-input');
        searchInput.value = text;
        this.hideAutocompleteSuggestions();

        // Store selected product info for quick access
        this.selectedProduct = { text, productId };

        // Perform search or use quick search for faster results
        if (productId) {
            this.performQuickSearch(text);
        } else {
            this.performSearch(text);
        }
    }
    
    hideAutocompleteSuggestions() {
        const suggestionsContainer = document.getElementById('search-suggestions');
        suggestionsContainer.classList.remove('show');
    }
    
    displaySearchResults(results) {
        const resultsContainer = document.getElementById('search-results');
        
        let html = `
            <div class="results-section">
                <h3>🎯 Exact Matches (${results.exact_matches.length})</h3>
                <div class="results-grid">
                    ${results.exact_matches.map(product => this.createProductCard(product, 'exact')).join('')}
                </div>
            </div>
        `;
        
        if (results.high_confidence_similar.length > 0) {
            html += `
                <div class="results-section">
                    <h3>🔥 High-Confidence Similar (${results.high_confidence_similar.length})</h3>
                    <div class="results-grid">
                        ${results.high_confidence_similar.map(product => this.createProductCard(product, 'similar')).join('')}
                    </div>
                </div>
            `;
        }
        
        if (results.medium_confidence_similar.length > 0) {
            html += `
                <div class="results-section">
                    <h3>⚡ Similar Products (${results.medium_confidence_similar.length})</h3>
                    <div class="results-grid">
                        ${results.medium_confidence_similar.map(product => this.createProductCard(product, 'medium')).join('')}
                    </div>
                </div>
            `;
        }
        
        resultsContainer.innerHTML = html;
        resultsContainer.classList.add('show');
    }
    
    createProductCard(product, type) {
        const similarityScore = (product.similarity_score * 100).toFixed(1);
        const hasUrl = product.product_url && product.product_url.trim() !== '';
        
        return `
            <div class="product-card">
                <div class="product-name">${product.name}</div>
                <div class="product-meta">
                    <div class="product-brand">🏷️ ${product.brand}</div>
                    <div class="product-mpn">🔢 MPN: ${product.mpn}</div>
                    <div class="product-category">📂 ${product.category}</div>
                    ${product.seller ? `<div class="product-seller">🏪 ${product.seller}</div>` : ''}
                </div>
                <div class="similarity-score">Similarity: ${similarityScore}%</div>
                <div class="product-actions">
                    <button class="btn btn-primary btn-sm" onclick="app.addToWishlist('${product.product_id}')">
                        Add to Wishlist
                    </button>
                    ${hasUrl ? `<a href="${product.product_url}" target="_blank" class="btn btn-outline btn-sm">View Product</a>` : ''}
                </div>
            </div>
        `;
    }
    
    hideSearchResults() {
        const resultsContainer = document.getElementById('search-results');
        resultsContainer.classList.remove('show');
    }
    
    // Wishlist Management
    async addToWishlistFromSuggestion(productId, productName, brand, mpn, category, productUrl = '') {
        this.showLoading('Adding to wishlist...');

        try {
            const response = await fetch('/api/wishlist/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Session-Token': this.sessionToken
                },
                body: JSON.stringify({
                    product_id: productId,
                    mpn: mpn,
                    product_name: productName,
                    brand: brand,
                    category: category,
                    seller: '', // Not available in autocomplete
                    product_url: productUrl
                })
            });

            if (response.ok) {
                const result = await response.json();
                this.showSuccess('Product added to wishlist!');
                this.hideAutocompleteSuggestions();
                this.loadWishlist(); // Refresh wishlist display
            } else {
                const error = await response.json();
                this.showError(error.detail || 'Failed to add product to wishlist');
            }
        } catch (error) {
            console.error('Error adding to wishlist:', error);
            this.showError('Failed to add product to wishlist');
        } finally {
            this.hideLoading();
        }
    }

    async addToWishlist(productId) {
        if (!this.currentSearchResults) return;

        // Find the product in current search results
        let product = null;
        for (const category of ['exact_matches', 'high_confidence_similar', 'medium_confidence_similar', 'category_related']) {
            const found = this.currentSearchResults[category]?.find(p => p.product_id === productId);
            if (found) {
                product = found;
                break;
            }
        }

        if (!product) return;
        
        this.showLoading('Adding to wishlist...');
        
        try {
            const response = await fetch('/api/wishlist/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Session-Token': this.sessionToken
                },
                body: JSON.stringify({
                    product_id: product.product_id,
                    mpn: product.mpn,
                    product_name: product.name,
                    brand: product.brand,
                    category: product.category,
                    subcategory: product.subcategory,
                    seller: product.seller,
                    price: product.price,
                    product_url: product.product_url,
                    search_query: this.currentSearchResults.query,
                    similarity_score: product.similarity_score,
                    similarity_distance: product.similarity_distance
                })
            });
            
            if (response.ok) {
                this.showSuccess('Product added to wishlist!');
                this.loadWishlist();
            } else {
                this.showError('Failed to add product to wishlist.');
            }
        } catch (error) {
            console.error('Add to wishlist error:', error);
            this.showError('Failed to add product to wishlist.');
        } finally {
            this.hideLoading();
        }
    }
    
    async loadWishlist() {
        try {
            const response = await fetch('/api/wishlist/', {
                headers: {
                    'X-Session-Token': this.sessionToken
                }
            });
            
            if (response.ok) {
                const wishlistData = await response.json();
                this.wishlist = wishlistData.items;
                this.displayWishlist(wishlistData);
            }
        } catch (error) {
            console.error('Load wishlist error:', error);
        }
    }
    
    displayWishlist(wishlistData) {
        const wishlistContainer = document.getElementById('wishlist-items');
        
        if (wishlistData.items.length === 0) {
            wishlistContainer.innerHTML = '<p class="text-center">Your wishlist is empty. Search for products to add them!</p>';
            return;
        }
        
        const html = wishlistData.items.map(item => `
            <div class="wishlist-item">
                <div class="wishlist-item-info">
                    <div class="wishlist-item-name">${item.product_name}</div>
                    <div class="wishlist-item-meta">
                        🏷️ ${item.brand} • 🔢 ${item.mpn} • 📂 ${item.category}
                        ${item.price ? ` • 💰 $${item.price}` : ''}
                    </div>
                </div>
                <div class="wishlist-item-actions">
                    ${item.product_url ? `<a href="${item.product_url}" target="_blank" class="btn btn-outline btn-sm">View</a>` : ''}
                    <button class="btn btn-secondary btn-sm" onclick="app.removeFromWishlist('${item.id}')">Remove</button>
                </div>
            </div>
        `).join('');
        
        wishlistContainer.innerHTML = html;
        
        // Update wishlist summary
        document.querySelector('.wishlist-container h2').textContent = 
            `Your Wishlist (${wishlistData.total_items} items)`;
    }
    
    async removeFromWishlist(itemId) {
        this.showLoading('Removing from wishlist...');
        
        try {
            const response = await fetch(`/api/wishlist/${itemId}`, {
                method: 'DELETE',
                headers: {
                    'X-Session-Token': this.sessionToken
                }
            });
            
            if (response.ok) {
                this.showSuccess('Product removed from wishlist!');
                this.loadWishlist();
            } else {
                this.showError('Failed to remove product from wishlist.');
            }
        } catch (error) {
            console.error('Remove from wishlist error:', error);
            this.showError('Failed to remove product from wishlist.');
        } finally {
            this.hideLoading();
        }
    }
    
    async clearWishlist() {
        if (!confirm('Are you sure you want to clear your entire wishlist?')) {
            return;
        }
        
        this.showLoading('Clearing wishlist...');
        
        try {
            const response = await fetch('/api/wishlist/', {
                method: 'DELETE',
                headers: {
                    'X-Session-Token': this.sessionToken
                }
            });
            
            if (response.ok) {
                this.showSuccess('Wishlist cleared!');
                this.loadWishlist();
                this.hideComparisonResults();
            } else {
                this.showError('Failed to clear wishlist.');
            }
        } catch (error) {
            console.error('Clear wishlist error:', error);
            this.showError('Failed to clear wishlist.');
        } finally {
            this.hideLoading();
        }
    }
    
    // Price Comparison
    async comparePrices() {
        if (this.wishlist.length === 0) {
            this.showError('Add some products to your wishlist first!');
            return;
        }
        
        this.showLoading('Comparing prices...');
        
        try {
            const response = await fetch('/api/wishlist/compare-prices', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Session-Token': this.sessionToken
                }
            });
            
            if (response.ok) {
                const comparisonData = await response.json();
                this.displayComparisonResults(comparisonData);
            } else {
                this.showError('Failed to compare prices.');
            }
        } catch (error) {
            console.error('Price comparison error:', error);
            this.showError('Failed to compare prices.');
        } finally {
            this.hideLoading();
        }
    }
    
    displayComparisonResults(comparisonData) {
        const comparisonContainer = document.getElementById('comparison-results');
        
        if (comparisonData.comparison_results.length === 0) {
            comparisonContainer.innerHTML = '<p class="text-center">No comparison data available.</p>';
            comparisonContainer.classList.add('show');
            return;
        }
        
        let html = `
            <div class="comparison-summary mb-4">
                <h3>Comparison Summary</h3>
                <p>Found alternatives for ${comparisonData.items_with_alternatives} out of ${comparisonData.total_items} items.</p>
                ${comparisonData.potential_total_savings > 0 ? 
                    `<p class="text-success">Potential total savings: $${comparisonData.potential_total_savings.toFixed(2)}</p>` : 
                    ''
                }
            </div>
        `;
        
        html += comparisonData.comparison_results.map(result => {
            const item = result.wishlist_item;
            const alternatives = result.alternatives || [];
            
            return `
                <div class="comparison-item">
                    <div class="comparison-header">
                        <h4>${item.name}</h4>
                        <p>🏷️ ${item.brand} • 🔢 ${item.mpn} ${item.price ? `• 💰 $${item.price}` : ''}</p>
                    </div>
                    <div class="comparison-alternatives">
                        ${alternatives.length > 0 ? 
                            `<h5>Found ${alternatives.length} alternatives:</h5>
                            ${alternatives.map(alt => this.createAlternativeCard(alt, item)).join('')}` :
                            '<p>No alternatives found for this product.</p>'
                        }
                    </div>
                </div>
            `;
        }).join('');
        
        comparisonContainer.innerHTML = html;
        comparisonContainer.classList.add('show');
        
        // Scroll to comparison section
        document.getElementById('comparison-section').scrollIntoView({ behavior: 'smooth' });
    }
    
    createAlternativeCard(alternative, originalItem) {
        const hasPrice = alternative.price !== null && alternative.price !== undefined;
        const hasSavings = hasPrice && originalItem.price && alternative.price < originalItem.price;
        const savings = hasSavings ? originalItem.price - alternative.price : 0;
        const savingsPercent = hasSavings ? ((savings / originalItem.price) * 100).toFixed(1) : 0;
        
        return `
            <div class="alternative-card">
                <div class="alternative-info">
                    <div class="alternative-name">${alternative.name}</div>
                    <div class="alternative-meta">
                        🏷️ ${alternative.brand} • 🏪 ${alternative.seller} • 
                        Match: ${(alternative.match_confidence * 100).toFixed(1)}%
                    </div>
                </div>
                <div class="alternative-pricing">
                    ${hasPrice ? `<div class="alternative-price">$${alternative.price}</div>` : '<div class="alternative-price">Price not available</div>'}
                    ${hasSavings ? `<div class="savings-badge">Save $${savings.toFixed(2)} (${savingsPercent}%)</div>` : ''}
                </div>
                <div class="alternative-actions">
                    ${alternative.product_url ? `<a href="${alternative.product_url}" target="_blank" class="btn btn-outline btn-sm">View Product</a>` : ''}
                </div>
            </div>
        `;
    }
    
    hideComparisonResults() {
        const comparisonContainer = document.getElementById('comparison-results');
        comparisonContainer.classList.remove('show');
    }
    
    // UI Helpers
    showLoading(message = 'Loading...') {
        const overlay = document.getElementById('loading-overlay');
        const text = overlay.querySelector('p');
        text.textContent = message;
        overlay.classList.add('show');
    }
    
    hideLoading() {
        const overlay = document.getElementById('loading-overlay');
        overlay.classList.remove('show');
    }
    
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showError(message) {
        this.showNotification(message, 'error');
    }
    
    showNotification(message, type = 'info') {
        // Simple notification system
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: 500;
            z-index: 1001;
            background: ${type === 'success' ? 'var(--success-color)' : type === 'error' ? 'var(--error-color)' : 'var(--primary-color)'};
            box-shadow: var(--shadow-lg);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new ProductSearchApp();
});
